<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游戏结果 - 斗地主计分助手</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="phone-container">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="status-time">14:30</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <div></div>
                <div class="nav-title">游戏结果</div>
                <div></div>
            </div>

            <!-- 主内容 -->
            <div class="content">
                <!-- 游戏结果标题 -->
                <div class="result-header fade-in">
                    <div class="result-icon" id="resultIcon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <h2 id="resultTitle">游戏结束</h2>
                    <p id="resultSubtitle">恭喜获胜方！</p>
                </div>

                <!-- 游戏详情 -->
                <div class="card fade-in">
                    <div class="card-title">
                        <i class="fas fa-info-circle" style="color: #007AFF; margin-right: 8px;"></i>
                        游戏详情
                    </div>
                    <div class="game-details">
                        <div class="detail-row">
                            <span class="detail-label">游戏时间:</span>
                            <span id="gameTime"></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">游戏模式:</span>
                            <span id="gameMode"></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">底分:</span>
                            <span id="baseScore"></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">最终倍数:</span>
                            <span id="finalMultiplier" style="color: #DC143C; font-weight: 600;"></span>
                        </div>
                        <div class="detail-row" id="specialInfoRow" style="display: none;">
                            <span class="detail-label">特殊情况:</span>
                            <span id="specialInfo" style="color: #FF6B6B; font-weight: 600;"></span>
                        </div>
                    </div>
                </div>

                <!-- 玩家得分 -->
                <div class="card fade-in">
                    <div class="card-title">
                        <i class="fas fa-users" style="color: #28A745; margin-right: 8px;"></i>
                        玩家得分
                    </div>
                    <div id="playerScores">
                        <!-- 玩家得分将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="action-buttons fade-in">
                    <button class="btn btn-secondary btn-large" onclick="Navigation.goTo('game-setup.html')" style="flex: 1;">
                        <i class="fas fa-redo" style="margin-right: 8px;"></i>
                        再来一局
                    </button>
                    <button class="btn btn-primary btn-large" onclick="Navigation.goTo('index.html')" style="flex: 1;">
                        <i class="fas fa-home" style="margin-right: 8px;"></i>
                        返回首页
                    </button>
                </div>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator">
                <div class="home-bar"></div>
            </div>
        </div>
    </div>

    <style>
        .result-header {
            text-align: center;
            padding: 32px 20px;
            background: linear-gradient(135deg, #FFF8DC, #FFFACD);
            border-radius: 20px;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
        }

        .result-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="confetti" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="2" cy="2" r="1" fill="%23FFD700" opacity="0.3"/><circle cx="18" cy="18" r="1" fill="%23FF6B6B" opacity="0.3"/><circle cx="10" cy="15" r="1" fill="%2328A745" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23confetti)"/></svg>');
            opacity: 0.1;
            pointer-events: none;
        }

        .result-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            font-size: 36px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.15);
            position: relative;
            z-index: 1;
            animation: bounceIn 0.8s ease-out;
        }

        @keyframes bounceIn {
            0% { transform: scale(0.3); opacity: 0; }
            50% { transform: scale(1.05); }
            70% { transform: scale(0.9); }
            100% { transform: scale(1); opacity: 1; }
        }

        .result-icon.winner {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #8B0000;
        }

        .result-icon.loser {
            background: linear-gradient(135deg, #DC3545, #B02A37);
            color: white;
        }

        .result-header h2 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            color: #333;
            position: relative;
            z-index: 1;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .result-header p {
            font-size: 16px;
            color: #666;
            margin: 0;
            position: relative;
            z-index: 1;
        }

        .game-details {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            background: linear-gradient(135deg, #F8F9FA, #E9ECEF);
            border-radius: 12px;
            border-left: 4px solid #007AFF;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .detail-row::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s ease;
        }

        .detail-row:hover::before {
            left: 100%;
        }

        .detail-row:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .detail-label {
            font-size: 15px;
            color: #666;
            font-weight: 600;
        }

        #playerScores {
            padding: 8px 0;
        }

        #playerScores::before {
            content: '';
            display: block;
            height: 1px;
            background: linear-gradient(90deg, transparent, #E9ECEF, transparent);
            margin: 8px 0 16px 0;
        }

        .detail-value {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .multiplier-value {
            color: #DC143C !important;
            font-size: 18px;
            font-weight: 700;
        }

        .player-score-item {
            display: flex;
            align-items: center;
            padding: 24px 20px;
            border-radius: 20px;
            margin-bottom: 20px;
            background: #FFFFFF;
            border: 2px solid #F0F0F0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            min-height: 100px;
        }

        .player-score-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
            transition: left 0.6s ease;
        }

        .player-score-item:hover::before {
            left: 100%;
        }

        .player-score-item:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 12px 32px rgba(0,0,0,0.15);
            border-color: #007AFF;
        }

        .player-score-item:last-child {
            margin-bottom: 0;
        }

        .player-score-item.winner {
            background: linear-gradient(135deg, #FFF8DC 0%, #FFFACD 50%, #FFF8DC 100%);
            border: 3px solid #FFD700;
            box-shadow: 0 8px 32px rgba(255,215,0,0.4);
            position: relative;
            animation: winnerGlow 2s ease-in-out infinite alternate;
        }

        @keyframes winnerGlow {
            0% { box-shadow: 0 8px 32px rgba(255,215,0,0.4); }
            100% { box-shadow: 0 12px 40px rgba(255,215,0,0.6); }
        }

        .player-score-item.winner::after {
            content: '👑';
            position: absolute;
            top: -12px;
            right: -8px;
            font-size: 24px;
            background: linear-gradient(135deg, #FFD700, #FFA500);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 16px rgba(255,215,0,0.5);
            border: 2px solid #FFF;
            animation: crownBounce 1.5s ease-in-out infinite;
        }

        @keyframes crownBounce {
            0%, 100% { transform: translateY(0) rotate(0deg); }
            50% { transform: translateY(-3px) rotate(5deg); }
        }

        .player-info {
            display: flex;
            flex-direction: column;
            justify-content: center;
            flex: 1;
            margin-left: 8px;
            margin-right: 16px;
        }

        .player-name-row {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 8px;
        }

        .player-name {
            font-size: 16px;
            font-weight: 700;
            color: #333;
            letter-spacing: 0.3px;
        }

        .player-role {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 10px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            white-space: nowrap;
        }

        .role-landlord {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #8B0000;
            border: 1px solid #FFD700;
        }

        .role-farmer {
            background: linear-gradient(135deg, #28A745, #20C997);
            color: white;
            border: 1px solid #28A745;
        }

        .player-status {
            font-size: 13px;
            color: #666;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .winner-badge {
            color: #FFD700;
            font-weight: 700;
            animation: sparkle 1.5s ease-in-out infinite;
        }

        @keyframes sparkle {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .score-display {
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 80px;
            height: 50px;
            border-radius: 16px;
            font-size: 20px;
            font-weight: 800;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
            flex-shrink: 0;
        }

        .score-display::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: inherit;
            filter: blur(10px);
            opacity: 0.3;
            z-index: -1;
        }

        .score-positive {
            background: linear-gradient(135deg, #D4EDDA 0%, #C3E6CB 50%, #D4EDDA 100%);
            color: #155724;
            border-color: #28A745;
            text-shadow: 0 1px 2px rgba(21,87,36,0.2);
        }

        .score-negative {
            background: linear-gradient(135deg, #F8D7DA 0%, #F5C6CB 50%, #F8D7DA 100%);
            color: #721C24;
            border-color: #DC3545;
            text-shadow: 0 1px 2px rgba(114,28,36,0.2);
        }

        .score-zero {
            background: linear-gradient(135deg, #F8F9FA 0%, #E9ECEF 50%, #F8F9FA 100%);
            color: #6C757D;
            border-color: #DEE2E6;
        }

        .action-buttons {
            display: flex;
            gap: 16px;
            margin-top: 24px;
        }

        .btn-large {
            padding: 18px 28px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            border: none;
            cursor: pointer;
        }

        .btn-large::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn-large:hover::before {
            left: 100%;
        }

        .btn-large:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 24px rgba(0,0,0,0.2);
        }

        .btn-large:active {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }


    </style>

    <script src="js/common.js"></script>
    <script>
        // 页面加载完成后显示结果
        document.addEventListener('DOMContentLoaded', function() {
            displayGameResult();
        });

        function displayGameResult() {
            // 尝试从不同的localStorage键获取游戏结果
            let gameResult = LocalStorage.getItem('latestGameResult') || LocalStorage.getItem('gameResult');

            if (!gameResult) {
                Utils.showToast('未找到游戏结果', 'error');
                Navigation.goTo('index.html');
                return;
            }

            const players = PlayerManager.getPlayers();
            const landlordPlayer = players.find(p => p.id === gameResult.landlord);

            // 设置结果标题和图标
            const resultIcon = document.getElementById('resultIcon');
            const resultTitle = document.getElementById('resultTitle');
            const resultSubtitle = document.getElementById('resultSubtitle');

            if (gameResult.isLandlordWin) {
                resultIcon.innerHTML = '<i class="fas fa-crown"></i>';
                resultIcon.className = 'result-icon winner';
                resultTitle.textContent = '地主获胜！';
                resultSubtitle.textContent = `恭喜 ${landlordPlayer ? landlordPlayer.name : '地主'} 获得胜利！`;

                // 添加胜利特效
                setTimeout(() => {
                    createConfetti();
                }, 500);
            } else {
                resultIcon.innerHTML = '<i class="fas fa-users"></i>';
                resultIcon.className = 'result-icon winner';
                resultTitle.textContent = '农民获胜！';
                resultSubtitle.textContent = '农民联盟获得胜利！';

                // 添加胜利特效
                setTimeout(() => {
                    createConfetti();
                }, 500);
            }

            // 显示游戏详情
            document.getElementById('gameTime').textContent = Utils.formatDate(gameResult.timestamp || new Date().toISOString());
            document.getElementById('gameMode').textContent =
                gameResult.gameMode === 'classic' ? '经典斗地主' : '癞子斗地主';
            document.getElementById('baseScore').textContent = gameResult.baseScore + '分';
            document.getElementById('finalMultiplier').textContent = gameResult.multiplier + '倍';

            // 显示特殊情况
            const specialInfoEl = document.getElementById('specialInfo');
            const specialInfoRow = document.getElementById('specialInfoRow');
            if (specialInfoEl && specialInfoRow) {
                const specialInfo = [];
                if (gameResult.bombCount > 0) {
                    specialInfo.push(`💣 炸弹×${gameResult.bombCount}`);
                }
                if (gameResult.isSpring) {
                    specialInfo.push('🌸 春天');
                }
                if (gameResult.isAntiSpring) {
                    specialInfo.push('🌸 反春天');
                }
                if (gameResult.isShowCard) {
                    specialInfo.push('🃏 明牌');
                }

                if (specialInfo.length > 0) {
                    specialInfoEl.innerHTML = specialInfo.join(' • ');
                    specialInfoRow.style.display = 'flex';
                } else {
                    specialInfoRow.style.display = 'none';
                }
            }

            // 显示玩家得分
            displayPlayerScores(gameResult, players);

            // 添加交错动画效果
            setTimeout(() => {
                addStaggeredAnimations();
            }, 100);
        }

        function displayPlayerScores(gameResult, players) {
            const playerScoresContainer = document.getElementById('playerScores');

            // 按得分排序玩家
            const sortedScores = gameResult.scores.sort((a, b) => b.score - a.score);

            playerScoresContainer.innerHTML = sortedScores.map((scoreData, index) => {
                const player = players.find(p => p.id === scoreData.playerId);
                if (!player) return '';

                const isLandlord = scoreData.playerId === gameResult.landlord;
                const score = scoreData.score;
                const isWinner = (isLandlord && gameResult.isLandlordWin) || (!isLandlord && !gameResult.isLandlordWin);

                return `
                    <div class="player-score-item ${isWinner ? 'winner' : ''}">
                        <div class="avatar" style="background: ${getPlayerAvatarColor(player)}; width: 48px; height: 48px; border-radius: 24px; display: flex; align-items: center; justify-content: center; margin-right: 20px; box-shadow: 0 4px 16px rgba(0,0,0,0.2); border: 2px solid #fff; flex-shrink: 0;">
                            ${getAvatarIcon(player.avatar)}
                        </div>
                        <div class="player-info">
                            <div class="player-name-row">
                                <span class="player-name">${player.name}</span>
                                ${isLandlord ? '<span class="player-role role-landlord">地主</span>' : '<span class="player-role role-farmer">农民</span>'}
                            </div>
                            <div class="player-status">
                                ${isWinner ? '<span class="winner-badge">🏆 获胜者</span>' : '<span style="color: #999;">参与者</span>'}
                            </div>
                        </div>
                        <div class="score-display ${score > 0 ? 'score-positive' : score < 0 ? 'score-negative' : 'score-zero'}">
                            ${score > 0 ? '+' : ''}${score}
                        </div>
                    </div>
                `;
            }).join('');
        }

        function getPlayerAvatarColor(player) {
            const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#F8B500', '#FF8C94'];
            const index = player.name.charCodeAt(0) % colors.length;
            return colors[index];
        }

        function getAvatarIcon(avatarType) {
            switch(avatarType) {
                case 'landlord':
                    return '<i class="fas fa-crown" style="color: #FFD700; font-size: 18px; text-shadow: 0 1px 2px rgba(0,0,0,0.3);"></i>';
                case 'farmer':
                    return '<i class="fas fa-user" style="color: #28A745; font-size: 18px; text-shadow: 0 1px 2px rgba(0,0,0,0.2);"></i>';
                default:
                    return '<i class="fas fa-user-circle" style="color: #6C757D; font-size: 18px;"></i>';
            }
        }

        function createConfetti() {
            // 创建彩带特效
            const colors = ['#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'];
            const confettiContainer = document.createElement('div');
            confettiContainer.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                pointer-events: none;
                z-index: 9999;
            `;
            document.body.appendChild(confettiContainer);

            for (let i = 0; i < 50; i++) {
                const confetti = document.createElement('div');
                confetti.style.cssText = `
                    position: absolute;
                    width: 10px;
                    height: 10px;
                    background: ${colors[Math.floor(Math.random() * colors.length)]};
                    top: -10px;
                    left: ${Math.random() * 100}%;
                    border-radius: 50%;
                    animation: confettiFall ${2 + Math.random() * 3}s linear forwards;
                `;
                confettiContainer.appendChild(confetti);
            }

            // 添加彩带下落动画
            const style = document.createElement('style');
            style.textContent = `
                @keyframes confettiFall {
                    to {
                        transform: translateY(100vh) rotate(720deg);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);

            // 3秒后清理
            setTimeout(() => {
                document.body.removeChild(confettiContainer);
                document.head.removeChild(style);
            }, 5000);
        }

        function addStaggeredAnimations() {
            // 为玩家卡片添加交错动画
            const playerItems = document.querySelectorAll('.player-score-item');
            playerItems.forEach((item, index) => {
                item.style.animationDelay = `${index * 0.15}s`;
                item.classList.add('animate-in');
            });

            // 为得分数字添加计数动画
            const scoreDisplays = document.querySelectorAll('.score-display');
            scoreDisplays.forEach((display, index) => {
                const finalScore = parseInt(display.textContent.replace(/[+\-]/g, ''));
                const isPositive = display.textContent.includes('+');
                const isNegative = display.textContent.includes('-');

                // 数字计数动画
                setTimeout(() => {
                    animateScore(display, finalScore, isPositive, isNegative);
                }, (index + 1) * 200);
            });
        }

        function animateScore(element, finalScore, isPositive, isNegative) {
            let currentScore = 0;
            const increment = Math.abs(finalScore) / 20;
            const isNegativeScore = finalScore < 0;

            const timer = setInterval(() => {
                if (Math.abs(currentScore) >= Math.abs(finalScore)) {
                    currentScore = finalScore;
                    clearInterval(timer);
                } else {
                    currentScore += isNegativeScore ? -increment : increment;
                }

                const displayValue = Math.round(currentScore);
                const prefix = displayValue > 0 ? '+' : displayValue < 0 ? '' : '';
                element.textContent = prefix + displayValue;
            }, 50);
        }
    </script>

    <style>
        /* 优化得分颜色显示 */
        .score.positive {
            color: #28A745;
            background: linear-gradient(135deg, #D4EDDA, #C3E6CB);
        }

        .score.negative {
            color: #DC3545;
            background: linear-gradient(135deg, #F8D7DA, #F5C6CB);
        }

        /* 卡片阴影效果 */
        .card {
            box-shadow: 0 4px 16px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 8px 24px rgba(0,0,0,0.12);
            transform: translateY(-2px);
        }

        /* 淡入动画 */
        .fade-in {
            animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
            opacity: 0;
        }

        .fade-in:nth-child(2) { animation-delay: 0.15s; }
        .fade-in:nth-child(3) { animation-delay: 0.3s; }
        .fade-in:nth-child(4) { animation-delay: 0.45s; }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* 玩家卡片进入动画 */
        .player-score-item.animate-in {
            animation: slideInRight 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }

        .player-score-item {
            opacity: 0;
            transform: translateX(50px) scale(0.9);
        }

        .player-score-item.animate-in {
            opacity: 1;
            transform: translateX(0) scale(1);
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateX(0) scale(1);
            }
        }

        /* 得分数字动画 */
        .score-display {
            animation: scoreReveal 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }

        @keyframes scoreReveal {
            0% {
                transform: scale(0.5) rotate(-10deg);
                opacity: 0;
            }
            60% {
                transform: scale(1.1) rotate(2deg);
                opacity: 1;
            }
            100% {
                transform: scale(1) rotate(0deg);
                opacity: 1;
            }
        }

        /* 响应式优化 */
        @media (max-width: 480px) {
            .player-score-item {
                padding: 20px 16px;
                margin-bottom: 16px;
                min-height: 90px;
            }

            .avatar {
                width: 44px !important;
                height: 44px !important;
                margin-right: 16px !important;
            }

            .player-name {
                font-size: 15px;
            }

            .player-role {
                font-size: 9px;
                padding: 4px 8px;
            }

            .score-display {
                min-width: 70px;
                height: 45px;
                font-size: 18px;
            }

            .result-header {
                padding: 24px 16px;
            }

            .btn-large {
                padding: 14px 20px;
                font-size: 14px;
            }
        }

        @media (max-width: 375px) {
            .player-score-item {
                padding: 18px 14px;
                min-height: 85px;
            }

            .player-name-row {
                gap: 8px;
            }

            .player-info {
                margin-right: 12px;
            }
        }
    </style>
</body>
</html>
