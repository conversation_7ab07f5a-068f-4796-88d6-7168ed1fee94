<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游戏计分 - 斗地主计分助手</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="phone-container">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="status-time">14:30</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <button class="nav-button back" onclick="confirmExit()">
                    <i class="fas fa-chevron-left"></i> 退出
                </button>
                <div class="nav-title">游戏计分</div>
                <button class="nav-button" onclick="resetGame()">
                    <i class="fas fa-redo"></i>
                </button>
            </div>

            <!-- 主内容 -->
            <div class="content">
                <!-- 游戏信息 -->
                <div class="card fade-in">
                    <div class="game-info-header">
                        <div class="game-info-item">
                            <span class="game-info-label">游戏模式</span>
                            <span class="game-info-value" id="gameModeDisplay">经典斗地主</span>
                        </div>
                        <div class="game-info-item">
                            <span class="game-info-label">底分</span>
                            <span class="game-info-value" id="baseScoreDisplay" style="color: #DC143C;">1分</span>
                        </div>
                        <div class="game-info-item" id="capScoreItem" style="display: none;">
                            <span class="game-info-label">封顶</span>
                            <span class="game-info-value" id="capScoreDisplay" style="color: #FF6B35;">32倍</span>
                        </div>
                    </div>
                    <div class="current-multiplier">
                        <span>当前倍数</span>
                        <span class="multiplier-display" id="currentMultiplier">1</span>
                    </div>
                </div>

                <!-- 玩家角色选择 -->
                <div class="card fade-in">
                    <div class="card-title">
                        <i class="fas fa-crown" style="color: #FFD700; margin-right: 8px;"></i>
                        选择地主
                    </div>
                    <div id="landlordSelection" class="player-roles">
                        <!-- 玩家角色选择将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 倍数调整 -->
                <div class="card fade-in">
                    <div class="card-title">
                        <i class="fas fa-calculator" style="color: #28A745; margin-right: 8px;"></i>
                        倍数调整
                    </div>
                    <div class="multiplier-controls">
                        <div class="multiplier-row">
                            <span>叫分倍数:</span>
                            <div class="multiplier-buttons">
                                <button class="multiplier-btn" data-multiplier="1">1倍</button>
                                <button class="multiplier-btn" data-multiplier="2">2倍</button>
                                <button class="multiplier-btn" data-multiplier="3">3倍</button>
                            </div>
                        </div>
                        <div class="multiplier-row">
                            <span>炸弹:</span>
                            <div class="bomb-controls">
                                <button class="bomb-btn" onclick="adjustBombs(-1)">-</button>
                                <span id="bombCount">0</span>
                                <button class="bomb-btn" onclick="adjustBombs(1)">+</button>
                            </div>
                        </div>
                        <div class="multiplier-row">
                            <label class="checkbox-label">
                                <input type="checkbox" id="isSpring">
                                <span class="checkmark"></span>
                                春天
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" id="isAntiSpring">
                                <span class="checkmark"></span>
                                反春天
                            </label>
                        </div>
                        <div class="multiplier-row" id="showCardRow" style="display: none;">
                            <label class="checkbox-label">
                                <input type="checkbox" id="isShowCard">
                                <span class="checkmark"></span>
                                明牌 (×2)
                            </label>
                        </div>
                    </div>
                </div>

                <!-- 游戏结果 -->
                <div class="card fade-in">
                    <div class="card-title">
                        <i class="fas fa-trophy" style="color: #FFA500; margin-right: 8px;"></i>
                        游戏结果
                    </div>
                    <div class="result-buttons">
                        <button class="result-btn landlord-win" onclick="setGameResult(true)">
                            <i class="fas fa-crown"></i>
                            <span>地主获胜</span>
                        </button>
                        <button class="result-btn farmer-win" onclick="setGameResult(false)">
                            <i class="fas fa-users"></i>
                            <span>农民获胜</span>
                        </button>
                    </div>
                </div>

                <!-- 结算按钮 -->
                <div class="action-buttons fade-in">
                    <button class="btn btn-primary btn-large" onclick="calculateScore()" id="calculateBtn" disabled>
                        <i class="fas fa-calculator" style="margin-right: 8px;"></i>
                        计算得分
                    </button>
                    <button class="btn btn-secondary btn-large" onclick="endGame()" id="endGameBtn">
                        <i class="fas fa-flag-checkered" style="margin-right: 8px;"></i>
                        结束对局
                    </button>
                </div>

                <!-- 计分历史 -->
                <div class="card fade-in" id="scoringHistory" style="display: none;">
                    <div class="card-title">
                        <i class="fas fa-history" style="color: #6C757D; margin-right: 8px;"></i>
                        本局计分历史
                        <button class="undo-btn" onclick="undoLastScore()" id="undoBtn" disabled>
                            <i class="fas fa-undo"></i> 撤销
                        </button>
                    </div>
                    <div class="scoring-history-list" id="scoringHistoryList">
                        <!-- 历史记录将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 总分统计 -->
                <div class="card fade-in" id="totalScores" style="display: none;">
                    <div class="card-title">
                        <i class="fas fa-chart-bar" style="color: #28A745; margin-right: 8px;"></i>
                        当前总分
                    </div>
                    <div class="total-scores-list" id="totalScoresList">
                        <!-- 总分统计将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator">
                <div class="home-bar"></div>
            </div>
        </div>
    </div>

    <style>
        .game-info-label {
            font-size: 14px;
            color: #666;
            margin-right: 8px;
        }

        .current-multiplier {
            padding: 12px;
            background: linear-gradient(135deg, #FFE4E1, #FFF0F5);
            border-radius: 8px;
            border: 2px solid #DC143C;
        }

        .player-roles {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .player-role-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 2px solid #E0E0E0;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .player-role-item:hover {
            border-color: #007AFF;
        }

        .player-role-item.landlord-selected {
            border-color: #FFD700;
            background: linear-gradient(135deg, #FFF8DC, #FFFACD);
        }

        .player-role-item .player-name {
            flex: 1;
            margin-left: 12px;
            font-size: 16px;
            font-weight: 600;
        }

        .multiplier-controls {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .multiplier-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .multiplier-buttons {
            display: flex;
            gap: 8px;
        }

        .multiplier-btn {
            padding: 8px 16px;
            border: 2px solid #E0E0E0;
            border-radius: 6px;
            background: white;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }

        .multiplier-btn:hover {
            border-color: #007AFF;
        }

        .multiplier-btn.selected {
            background: #007AFF;
            color: white;
            border-color: #007AFF;
        }

        .bomb-controls {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .bomb-btn {
            width: 36px;
            height: 36px;
            border: 2px solid #DC143C;
            border-radius: 8px;
            background: white;
            color: #DC143C;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .bomb-btn:hover {
            background: #DC143C;
            color: white;
        }

        #bombCount {
            font-size: 18px;
            font-weight: 600;
            color: #DC143C;
            min-width: 20px;
            text-align: center;
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
        }

        .checkbox-label input[type="checkbox"] {
            display: none;
        }

        .checkmark {
            width: 18px;
            height: 18px;
            border: 2px solid #DDD;
            border-radius: 4px;
            margin-right: 8px;
            position: relative;
            transition: all 0.2s;
        }

        .checkbox-label input[type="checkbox"]:checked + .checkmark {
            background: #28A745;
            border-color: #28A745;
        }

        .checkbox-label input[type="checkbox"]:checked + .checkmark::after {
            content: '✓';
            color: white;
            font-size: 12px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .result-buttons {
            display: flex;
            gap: 12px;
        }

        .result-btn {
            flex: 1;
            padding: 16px;
            border: 2px solid #E0E0E0;
            border-radius: 10px;
            background: white;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .result-btn:hover {
            border-color: #007AFF;
        }

        .result-btn.selected {
            border-color: #007AFF;
            background: rgba(0,122,255,0.1);
        }

        .landlord-win.selected {
            border-color: #FFD700;
            background: linear-gradient(135deg, #FFF8DC, #FFFACD);
        }

        .farmer-win.selected {
            border-color: #28A745;
            background: linear-gradient(135deg, #F0FFF0, #F5FFFA);
        }

        .result-btn i {
            font-size: 24px;
        }

        .landlord-win i {
            color: #FFD700;
        }

        .farmer-win i {
            color: #28A745;
        }

        .result-btn span {
            font-size: 14px;
            font-weight: 600;
        }

        .undo-btn {
            background: #6C757D;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
            margin-left: auto;
        }

        .undo-btn:hover:not(:disabled) {
            background: #5A6268;
            transform: translateY(-1px);
        }

        .undo-btn:disabled {
            background: #E9ECEF;
            color: #6C757D;
            cursor: not-allowed;
        }

        .scoring-history-list {
            max-height: 200px;
            overflow-y: auto;
        }

        .history-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            margin-bottom: 8px;
            background: #F8F9FA;
            border-radius: 8px;
            border-left: 4px solid #007AFF;
        }

        .history-item:last-child {
            margin-bottom: 0;
        }

        .history-details {
            flex: 1;
        }

        .history-round {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }

        .history-scores {
            display: flex;
            gap: 12px;
            font-size: 14px;
        }

        .history-score {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .history-score.positive {
            color: #28A745;
        }

        .history-score.negative {
            color: #DC3545;
        }

        .history-multiplier {
            font-size: 12px;
            color: #666;
            background: #E9ECEF;
            padding: 2px 6px;
            border-radius: 4px;
        }

        .cap-warning {
            color: #FF6B35;
            font-weight: bold;
            font-size: 12px;
            margin-left: 8px;
        }

        .action-buttons {
            display: flex;
            gap: 12px;
            margin-bottom: 16px;
        }

        .action-buttons .btn {
            flex: 1;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6C757D, #5A6268);
            color: white;
            border: none;
        }

        .btn-secondary:hover:not(:disabled) {
            background: linear-gradient(135deg, #5A6268, #495057);
            transform: translateY(-1px);
        }
    </style>

    <style>
        .game-info-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 16px;
            background: linear-gradient(135deg, #F8F9FA, #E9ECEF);
            border-radius: 10px;
        }

        .game-info-item {
            text-align: center;
        }

        .game-info-label {
            display: block;
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }

        .game-info-value {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .current-multiplier {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #FFF8DC, #FFFACD);
            border-radius: 12px;
            margin-top: 16px;
        }

        .current-multiplier span:first-child {
            font-size: 16px;
            color: #666;
        }

        .multiplier-display {
            font-size: 32px;
            font-weight: 700;
            color: #DC143C;
            margin-left: 8px;
        }

        .player-roles {
            display: grid;
            grid-template-columns: 1fr;
            gap: 12px;
        }

        .player-role-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border: 2px solid #E0E0E0;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.2s;
            background: white;
            margin-bottom: 8px;
        }

        .player-role-item:hover {
            border-color: #FFD700;
            background: rgba(255,215,0,0.1);
        }

        .player-role-item.landlord-selected {
            border-color: #FFD700;
            background: linear-gradient(135deg, #FFF8DC, #FFFACD);
        }

        .player-role-item .avatar {
            margin-right: 12px;
        }

        .player-role-item .player-name {
            flex: 1;
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .role-indicator {
            margin-left: auto;
        }

        .farmer-badge, .landlord-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }

        .farmer-badge {
            background: #28A745;
            color: white;
        }

        .landlord-badge {
            background: #FFD700;
            color: #8B0000;
        }

        .role-badge {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 600;
        }

        .landlord-role {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #8B0000;
        }

        .farmer-role {
            background: linear-gradient(135deg, #228B22, #32CD32);
            color: white;
        }

        .multiplier-controls {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .multiplier-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            background: linear-gradient(135deg, #F8F9FA, #E9ECEF);
            border-radius: 10px;
        }

        .multiplier-row > span {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            min-width: 80px;
        }

        .multiplier-buttons {
            display: flex;
            gap: 8px;
        }

        .multiplier-btn {
            padding: 8px 16px;
            border: 2px solid #E0E0E0;
            border-radius: 8px;
            background: white;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            color: #333;
            min-width: 50px;
        }

        .multiplier-btn:hover {
            border-color: #007AFF;
            background: rgba(0,122,255,0.05);
        }

        .multiplier-btn.selected {
            border-color: #DC143C;
            background: linear-gradient(135deg, #DC143C, #B22222);
            color: white;
        }

        .counter-control {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .counter-btn {
            width: 36px;
            height: 36px;
            border: 2px solid #E0E0E0;
            border-radius: 8px;
            background: white;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .counter-btn:hover {
            border-color: #007AFF;
            background: rgba(0,122,255,0.05);
        }

        .counter-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .counter-value {
            font-size: 18px;
            font-weight: 600;
            color: #DC143C;
            min-width: 24px;
            text-align: center;
        }

        .result-selection {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        .result-btn {
            padding: 20px 16px;
            border: 2px solid #E0E0E0;
            border-radius: 12px;
            background: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .result-btn i {
            font-size: 24px;
        }

        .result-btn.landlord-win {
            color: #8B0000;
        }

        .result-btn.landlord-win:hover {
            border-color: #FFD700;
            background: rgba(255,215,0,0.1);
        }

        .result-btn.landlord-win.selected {
            border-color: #FFD700;
            background: linear-gradient(135deg, #FFF8DC, #FFFACD);
        }

        .result-btn.farmer-win {
            color: #228B22;
        }

        .result-btn.farmer-win:hover {
            border-color: #28A745;
            background: rgba(40,167,69,0.1);
        }

        .result-btn.farmer-win.selected {
            border-color: #28A745;
            background: linear-gradient(135deg, #F0FFF0, #E6FFE6);
        }

        .score-preview {
            background: linear-gradient(135deg, #F8F9FA, #E9ECEF);
            border-radius: 12px;
            padding: 20px;
            margin-top: 16px;
        }

        .score-preview h4 {
            text-align: center;
            margin-bottom: 16px;
            color: #333;
            font-size: 18px;
        }

        .score-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #E0E0E0;
        }

        .score-item:last-child {
            border-bottom: none;
        }

        .score-player {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .score-value {
            font-size: 16px;
            font-weight: 600;
        }

        .score-positive {
            color: #28A745;
        }

        .score-negative {
            color: #DC3545;
        }

        .total-scores-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .total-score-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            background: linear-gradient(135deg, #F8F9FA, #E9ECEF);
            border-radius: 10px;
            border-left: 4px solid #007AFF;
        }

        .total-score-player {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .total-score-value {
            font-size: 18px;
            font-weight: 700;
        }

        .total-score-positive {
            color: #28A745;
        }

        .total-score-negative {
            color: #DC3545;
        }

        .total-score-zero {
            color: #6C757D;
        }
    </style>

    <script src="js/common.js"></script>
    <script>
        let gameData = {
            players: [],
            settings: {},
            landlord: null,
            callMultiplier: 1,
            bombCount: 0,
            isSpring: false,
            isAntiSpring: false,
            isShowCard: false,
            gameResult: null,
            scoringHistory: [],
            roundCounter: 1,
            totalScores: {} // 存储每个玩家的总分
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeGame();

            // 监听数据更新事件，实现实时同步
            window.addEventListener('playerDataUpdated', function() {
                // 重新检查玩家数据有效性
                validatePlayerData();
            });

            window.addEventListener('settingsUpdated', function() {
                // 重新加载游戏设置
                const settings = GameSettingsManager.getSettings();
                gameData.settings = settings;
                updateGameInfo();
            });
        });

        function initializeGame() {
            // 从localStorage获取游戏设置和玩家
            gameData.settings = LocalStorage.getItem('currentGameSettings');
            gameData.players = LocalStorage.getItem('currentGamePlayers');
            const gameSession = LocalStorage.getItem('currentGameSession');

            // 如果没有游戏数据，创建测试数据
            if (!gameData.settings || !gameData.players) {
                console.log('没有找到游戏数据，创建测试数据');

                // 确保有玩家数据
                const allPlayers = PlayerManager.getPlayers();
                if (allPlayers.length < 3) {
                    Utils.showToast('玩家数据不足，请先添加玩家', 'error');
                    Navigation.goTo('players.html');
                    return;
                }

                // 创建测试游戏数据
                gameData.settings = {
                    gameMode: 'classic',
                    baseScore: 1,
                    bombMultiplier: true,
                    springMultiplier: true,
                    showCardMultiplier: false,
                    capScore: 0
                };

                gameData.players = allPlayers.slice(0, 3).map(p => p.id);

                console.log('创建的测试数据:', { settings: gameData.settings, players: gameData.players });
            }

            // 验证玩家是否仍然存在
            const players = PlayerManager.getPlayers();
            const validPlayers = gameData.players.filter(playerId =>
                players.some(player => player.id === playerId)
            );

            if (validPlayers.length !== 3) {
                Utils.showToast('部分玩家已被删除，请重新设置游戏', 'error');
                Navigation.goTo('game-setup.html');
                return;
            }

            console.log('初始化游戏界面，玩家数据:', gameData.players);
            console.log('游戏设置:', gameData.settings);

            updateGameInfo();

            // 初始化总分
            initializeTotalScores();

            // 加载游戏会话数据
            loadGameSession();

            // 加载玩家列表
            loadPlayerRoles();

            // 初始化倍数按钮
            initializeMultiplierButtons();

            // 默认选择1倍
            setTimeout(() => {
                const firstMultiplierBtn = document.querySelector('[data-multiplier="1"]');
                if (firstMultiplierBtn) {
                    firstMultiplierBtn.classList.add('selected');
                    console.log('默认选择1倍');
                } else {
                    console.error('找不到1倍按钮');
                }
            }, 100);

            // 更新倍数显示
            updateMultiplierDisplay();
        }

        function initializeTotalScores() {
            // 尝试从游戏会话中加载总分
            const gameSession = LocalStorage.getItem('currentGameSession');
            if (gameSession && gameSession.totalScores) {
                gameData.totalScores = { ...gameSession.totalScores };
            } else {
                // 初始化每个玩家的总分为0
                gameData.players.forEach(playerId => {
                    gameData.totalScores[playerId] = 0;
                });
            }
            updateTotalScoresDisplay();
        }

        function saveGameSession() {
            // 保存当前游戏会话数据
            const gameSession = {
                players: gameData.players,
                totalScores: gameData.totalScores,
                scoringHistory: gameData.scoringHistory,
                roundCounter: gameData.roundCounter,
                settings: gameData.settings,
                startTime: LocalStorage.getItem('currentGameSession')?.startTime || new Date().toISOString(),
                lastUpdated: new Date().toISOString()
            };
            LocalStorage.setItem('currentGameSession', gameSession);
        }

        function loadGameSession() {
            // 加载游戏会话数据
            const gameSession = LocalStorage.getItem('currentGameSession');
            if (gameSession) {
                if (gameSession.totalScores) {
                    gameData.totalScores = { ...gameSession.totalScores };
                }
                if (gameSession.scoringHistory) {
                    gameData.scoringHistory = [...gameSession.scoringHistory];
                }
                if (gameSession.roundCounter) {
                    gameData.roundCounter = gameSession.roundCounter;
                }
                updateScoringHistory();
                updateTotalScoresDisplay();
            }
        }

        function updateMultiplierDisplay() {
            // 更新当前倍数显示
            updateCurrentMultiplier();

            // 更新计算按钮状态
            updateCalculateButton();
        }

        function updateGameInfo() {
            if (!gameData.settings) return;

            console.log('更新游戏信息:', gameData.settings);

            // 显示游戏信息 - 添加安全检查
            const gameModeEl = document.getElementById('gameModeDisplay');
            if (gameModeEl) {
                gameModeEl.textContent = gameData.settings.gameMode === 'classic' ? '经典斗地主' : '癞子斗地主';
            }

            const baseScoreEl = document.getElementById('baseScoreDisplay');
            if (baseScoreEl) {
                baseScoreEl.textContent = gameData.settings.baseScore + '分';
            }

            // 显示封顶分数
            const capScoreItem = document.getElementById('capScoreItem');
            const capScoreDisplay = document.getElementById('capScoreDisplay');
            if (capScoreItem && capScoreDisplay) {
                if (gameData.settings.capScore && gameData.settings.capScore > 0) {
                    capScoreItem.style.display = 'block';
                    capScoreDisplay.textContent = `${gameData.settings.capScore}倍`;
                } else {
                    capScoreItem.style.display = 'none';
                }
            }

            // 显示明牌选项
            const showCardRow = document.getElementById('showCardRow');
            if (showCardRow) {
                if (gameData.settings.showCardMultiplier) {
                    showCardRow.style.display = 'block';
                } else {
                    showCardRow.style.display = 'none';
                }
            }

            // 显示游戏会话信息 - 添加安全检查
            const gameTimeEl = document.getElementById('gameTimeDisplay');
            if (gameTimeEl) {
                const gameSession = LocalStorage.getItem('currentGameSession');
                if (gameSession) {
                    const startTime = new Date(gameSession.startTime);
                    gameTimeEl.textContent = Utils.formatTime(startTime);
                }
            }
        }

        function loadPlayerRoles() {
            const players = PlayerManager.getPlayers();
            const landlordSelectionContainer = document.getElementById('landlordSelection');

            console.log('加载玩家角色，所有玩家:', players);
            console.log('游戏中的玩家ID:', gameData.players);

            if (!gameData.players || gameData.players.length === 0) {
                console.error('没有游戏玩家数据');
                landlordSelectionContainer.innerHTML = '<p>没有玩家数据</p>';
                return;
            }

            const html = gameData.players.map(playerId => {
                const player = players.find(p => p.id === playerId);
                if (!player) {
                    console.warn('找不到玩家:', playerId);
                    return '';
                }

                const avatarIcon = getAvatarIcon(player.avatar);
                console.log('生成玩家HTML:', player.name);
                return `
                    <div class="player-role-item" data-player-id="${playerId}" onclick="selectLandlord('${playerId}')">
                        <div class="avatar">
                            ${avatarIcon}
                        </div>
                        <div class="player-name">${player.name}</div>
                        <div class="role-indicator" id="role_${playerId}">
                            <span class="farmer-badge">农民</span>
                        </div>
                    </div>
                `;
            }).join('');

            landlordSelectionContainer.innerHTML = html;
            console.log('玩家角色HTML已生成');
        }

        function getAvatarIcon(avatarType) {
            switch(avatarType) {
                case 'landlord':
                    return '<i class="fas fa-crown" style="color: #FFD700; font-size: 20px;"></i>';
                case 'farmer':
                    return '<i class="fas fa-user" style="color: #28A745; font-size: 20px;"></i>';
                default:
                    return '<i class="fas fa-user-circle" style="color: #6C757D; font-size: 20px;"></i>';
            }
        }

        function selectLandlord(playerId) {
            // 重置所有玩家角色
            gameData.players.forEach(id => {
                const roleElement = document.getElementById(`role_${id}`);
                const playerItem = document.querySelector(`[data-player-id="${id}"]`);
                roleElement.innerHTML = '<span class="farmer-badge">农民</span>';
                playerItem.classList.remove('landlord-selected');
            });

            // 设置新地主
            gameData.landlord = playerId;
            const roleElement = document.getElementById(`role_${playerId}`);
            const playerItem = document.querySelector(`[data-player-id="${playerId}"]`);
            roleElement.innerHTML = '<span class="landlord-badge">地主</span>';
            playerItem.classList.add('landlord-selected');

            // 更新UI状态
            updateCalculateButton();
            updateMultiplierDisplay();

            Utils.showToast(`已选择地主`, 'success');
        }

        function initializeMultiplierButtons() {
            const multiplierBtns = document.querySelectorAll('.multiplier-btn');
            console.log('找到倍数按钮数量:', multiplierBtns.length);

            multiplierBtns.forEach((btn, index) => {
                console.log(`按钮${index}:`, btn.textContent, btn.dataset.multiplier);
                btn.addEventListener('click', function() {
                    console.log('点击倍数按钮:', this.dataset.multiplier);
                    document.querySelectorAll('.multiplier-btn').forEach(b => b.classList.remove('selected'));
                    this.classList.add('selected');
                    gameData.callMultiplier = parseInt(this.dataset.multiplier);
                    updateCurrentMultiplier();
                });
            });

            // 监听春天和反春天复选框
            document.getElementById('isSpring').addEventListener('change', function() {
                gameData.isSpring = this.checked;
                if (this.checked) {
                    document.getElementById('isAntiSpring').checked = false;
                    gameData.isAntiSpring = false;
                }
                updateCurrentMultiplier();
            });

            document.getElementById('isAntiSpring').addEventListener('change', function() {
                gameData.isAntiSpring = this.checked;
                if (this.checked) {
                    document.getElementById('isSpring').checked = false;
                    gameData.isSpring = false;
                }
                updateCurrentMultiplier();
            });

            // 监听明牌复选框
            const showCardCheckbox = document.getElementById('isShowCard');
            if (showCardCheckbox) {
                showCardCheckbox.addEventListener('change', function() {
                    gameData.isShowCard = this.checked;
                    updateCurrentMultiplier();
                });
            }
        }

        function adjustBombs(delta) {
            gameData.bombCount = Math.max(0, gameData.bombCount + delta);
            document.getElementById('bombCount').textContent = gameData.bombCount;
            updateCurrentMultiplier();
        }

        function updateCurrentMultiplier() {
            let multiplier = gameData.callMultiplier;

            // 炸弹倍数
            if (gameData.settings.bombMultiplier && gameData.bombCount > 0) {
                multiplier *= Math.pow(2, gameData.bombCount);
            }

            // 春天倍数
            if (gameData.settings.springMultiplier && (gameData.isSpring || gameData.isAntiSpring)) {
                multiplier *= 2;
            }

            // 明牌倍数
            if (gameData.settings.showCardMultiplier && gameData.isShowCard) {
                multiplier *= 2;
            }

            // 检查封顶分数
            let displayMultiplier = multiplier;
            let isCapReached = false;
            if (gameData.settings.capScore && gameData.settings.capScore > 0 && multiplier > gameData.settings.capScore) {
                displayMultiplier = gameData.settings.capScore;
                isCapReached = true;
            }

            const multiplierElement = document.getElementById('currentMultiplier');
            multiplierElement.textContent = displayMultiplier;

            // 显示封顶警告
            const existingWarning = multiplierElement.parentNode.querySelector('.cap-warning');
            if (existingWarning) {
                existingWarning.remove();
            }

            if (isCapReached) {
                const warning = document.createElement('span');
                warning.className = 'cap-warning';
                warning.textContent = '(已封顶)';
                multiplierElement.parentNode.appendChild(warning);
            }
        }

        function setGameResult(landlordWin) {
            gameData.gameResult = landlordWin;
            
            // 更新按钮状态
            document.querySelectorAll('.result-btn').forEach(btn => btn.classList.remove('selected'));
            if (landlordWin) {
                document.querySelector('.landlord-win').classList.add('selected');
            } else {
                document.querySelector('.farmer-win').classList.add('selected');
            }

            updateCalculateButton();
        }

        function updateCalculateButton() {
            const calculateBtn = document.getElementById('calculateBtn');
            if (gameData.landlord && gameData.gameResult !== null) {
                calculateBtn.disabled = false;
                calculateBtn.style.opacity = '1';
            } else {
                calculateBtn.disabled = true;
                calculateBtn.style.opacity = '0.5';
            }
        }

        function calculateScore() {
            if (!gameData.landlord || gameData.gameResult === null) {
                Utils.showToast('请选择地主和游戏结果', 'error');
                return;
            }

            console.log('开始计算得分:', {
                landlord: gameData.landlord,
                gameResult: gameData.gameResult,
                callMultiplier: gameData.callMultiplier,
                bombCount: gameData.bombCount,
                isSpring: gameData.isSpring,
                isAntiSpring: gameData.isAntiSpring,
                isShowCard: gameData.isShowCard
            });

            // 计算最终倍数
            let finalMultiplier = gameData.callMultiplier;

            // 炸弹倍数
            if (gameData.settings.bombMultiplier && gameData.bombCount > 0) {
                finalMultiplier *= Math.pow(2, gameData.bombCount);
            }

            // 春天/反春天倍数
            if (gameData.settings.springMultiplier && (gameData.isSpring || gameData.isAntiSpring)) {
                finalMultiplier *= 2;
            }

            // 明牌倍数
            if (gameData.settings.showCardMultiplier && gameData.isShowCard) {
                finalMultiplier *= 2;
            }

            // 应用封顶分数
            if (gameData.settings.capScore && gameData.settings.capScore > 0 && finalMultiplier > gameData.settings.capScore) {
                finalMultiplier = gameData.settings.capScore;
            }

            // 计算基础分数
            const baseScore = gameData.settings.baseScore;
            const totalScore = baseScore * finalMultiplier;

            // 创建游戏记录
            const gameRecord = {
                id: Utils.generateId(),
                timestamp: new Date().toISOString(),
                gameDate: new Date().toISOString().split('T')[0],
                landlord: gameData.landlord,
                players: [...gameData.players],
                baseScore: baseScore,
                callMultiplier: gameData.callMultiplier,
                bombCount: gameData.bombCount,
                isSpring: gameData.isSpring,
                isAntiSpring: gameData.isAntiSpring,
                isShowCard: gameData.isShowCard,
                multiplier: finalMultiplier,
                isLandlordWin: gameData.gameResult,
                scores: [],
                gameMode: gameData.settings.gameMode || 'classic'
            };

            // 计算每个玩家的得分
            const finalScores = {};
            gameData.players.forEach(playerId => {
                let playerScore;
                if (playerId === gameData.landlord) {
                    // 地主得分：地主赢得2倍分数，输失2倍分数
                    playerScore = gameData.gameResult ? totalScore * 2 : -totalScore * 2;
                } else {
                    // 农民得分：地主赢农民失分，地主输农民得分
                    playerScore = gameData.gameResult ? -totalScore : totalScore;
                }

                gameRecord.scores.push({
                    playerId: playerId,
                    score: playerScore,
                    isLandlord: playerId === gameData.landlord
                });

                finalScores[playerId] = playerScore;
            });

            // 添加finalScores字段以兼容旧版本
            gameRecord.finalScores = finalScores;

            console.log('计算结果:', {
                finalMultiplier: finalMultiplier,
                totalScore: totalScore,
                gameRecord: gameRecord
            });

            // 更新总分
            gameRecord.scores.forEach(scoreData => {
                gameData.totalScores[scoreData.playerId] += scoreData.score;
            });

            console.log('更新后的总分:', gameData.totalScores);

            // 添加到计分历史
            const historyItem = {
                round: gameData.roundCounter++,
                landlord: gameData.landlord,
                gameResult: gameData.gameResult,
                multiplier: finalMultiplier,
                scores: [...gameRecord.scores],
                timestamp: new Date().toISOString()
            };
            gameData.scoringHistory.push(historyItem);

            // 更新历史显示
            updateScoringHistory();
            updateTotalScoresDisplay();

            // 保存游戏会话
            saveGameSession();

            // 保存游戏记录
            try {
                GameRecordManager.addRecord(gameRecord);

                // 保存到localStorage用于结果页面显示
                LocalStorage.setItem('latestGameResult', gameRecord);
                LocalStorage.setItem('gameResult', gameRecord);

                Utils.showToast('计分完成！', 'success');

                // 重置游戏状态
                resetGameState();

                // 延迟跳转到结果页面
                setTimeout(() => {
                    Navigation.goTo('game-result.html');
                }, 1000);

            } catch (error) {
                console.error('保存游戏记录失败:', error);
                Utils.showToast('保存失败，请重试', 'error');
            }
        }

        function resetGame() {
            Utils.confirm('确定要重置当前游戏吗？', () => {
                gameData.landlord = null;
                gameData.callMultiplier = 1;
                gameData.bombCount = 0;
                gameData.isSpring = false;
                gameData.isAntiSpring = false;
                gameData.gameResult = null;

                // 重置界面
                document.querySelectorAll('.multiplier-btn').forEach(btn => btn.classList.remove('selected'));
                document.querySelector('[data-multiplier="1"]').classList.add('selected');
                document.getElementById('bombCount').textContent = '0';
                document.getElementById('isSpring').checked = false;
                document.getElementById('isAntiSpring').checked = false;
                document.querySelectorAll('.result-btn').forEach(btn => btn.classList.remove('selected'));

                loadPlayerRoles();
                updateCurrentMultiplier();
                updateCalculateButton();

                Utils.showToast('游戏已重置');
            });
        }

        function confirmExit() {
            Utils.confirm('确定要退出当前游戏吗？游戏数据将不会保存。', () => {
                Navigation.goTo('index.html');
            });
        }

        function updateScoringHistory() {
            const historyContainer = document.getElementById('scoringHistoryList');
            const historyCard = document.getElementById('scoringHistory');
            const undoBtn = document.getElementById('undoBtn');

            if (gameData.scoringHistory.length === 0) {
                historyCard.style.display = 'none';
                return;
            }

            historyCard.style.display = 'block';
            undoBtn.disabled = false;

            historyContainer.innerHTML = gameData.scoringHistory.map(item => {
                const players = PlayerManager.getPlayers();

                return `
                    <div class="history-item">
                        <div class="history-details">
                            <div class="history-round">第${item.round}局 - ${item.gameResult ? '地主胜' : '农民胜'}</div>
                            <div class="history-scores">
                                ${item.scores.map(score => {
                                    const playerName = players.find(p => p.id === score.playerId)?.name || '未知';
                                    return `
                                        <div class="history-score ${score.score > 0 ? 'positive' : 'negative'}">
                                            ${playerName}: ${score.score > 0 ? '+' : ''}${score.score}
                                        </div>
                                    `;
                                }).join('')}
                            </div>
                        </div>
                        <div class="history-multiplier">${item.multiplier}倍</div>
                    </div>
                `;
            }).reverse().join('');
        }

        function updateTotalScoresDisplay() {
            const totalScoresCard = document.getElementById('totalScores');
            const totalScoresList = document.getElementById('totalScoresList');

            if (gameData.scoringHistory.length === 0) {
                totalScoresCard.style.display = 'none';
                return;
            }

            totalScoresCard.style.display = 'block';
            const players = PlayerManager.getPlayers();

            totalScoresList.innerHTML = gameData.players.map(playerId => {
                const player = players.find(p => p.id === playerId);
                const totalScore = gameData.totalScores[playerId] || 0;
                const avatarIcon = getAvatarIcon(player?.avatar);

                let scoreClass = 'total-score-zero';
                if (totalScore > 0) scoreClass = 'total-score-positive';
                else if (totalScore < 0) scoreClass = 'total-score-negative';

                return `
                    <div class="total-score-item">
                        <div class="total-score-player">
                            <div class="avatar">${avatarIcon}</div>
                            <span class="player-name">${player?.name || '未知玩家'}</span>
                        </div>
                        <div class="total-score-value ${scoreClass}">
                            ${totalScore > 0 ? '+' : ''}${totalScore}
                        </div>
                    </div>
                `;
            }).join('');
        }

        function undoLastScore() {
            if (gameData.scoringHistory.length === 0) {
                Utils.showToast('没有可撤销的记录', 'error');
                return;
            }

            Utils.confirm('确定要撤销上一局的计分吗？', () => {
                // 移除最后一条历史记录
                const lastItem = gameData.scoringHistory.pop();
                gameData.roundCounter--;

                // 恢复总分
                lastItem.scores.forEach(scoreData => {
                    gameData.totalScores[scoreData.playerId] -= scoreData.score;
                });

                // 从游戏记录中移除对应的记录
                const records = GameRecordManager.getRecords();
                const recordToRemove = records.find(record =>
                    record.createTime === lastItem.timestamp
                );

                if (recordToRemove) {
                    GameRecordManager.deleteRecord(recordToRemove.id);
                }

                // 更新显示
                updateScoringHistory();
                updateTotalScoresDisplay();

                // 保存游戏会话
                saveGameSession();

                Utils.showToast('已撤销上一局计分', 'success');
            });
        }

        function resetGameState() {
            // 重置游戏状态但保留地主选择
            gameData.callMultiplier = 1;
            gameData.bombCount = 0;
            gameData.isSpring = false;
            gameData.isAntiSpring = false;
            gameData.isShowCard = false;
            gameData.gameResult = null;

            // 重置UI
            document.getElementById('bombCount').textContent = '0';
            document.getElementById('isSpring').checked = false;
            document.getElementById('isAntiSpring').checked = false;
            const showCardCheckbox = document.getElementById('isShowCard');
            if (showCardCheckbox) {
                showCardCheckbox.checked = false;
            }

            // 重置倍数按钮
            document.querySelectorAll('.multiplier-btn').forEach(btn => btn.classList.remove('selected'));
            document.querySelector('[data-multiplier="1"]').classList.add('selected');

            // 重置结果按钮
            document.querySelectorAll('.result-btn').forEach(btn => btn.classList.remove('selected'));

            // 更新显示
            updateCurrentMultiplier();
            updateCalculateButton();
        }

        function endGame() {
            if (gameData.scoringHistory.length === 0) {
                Utils.confirm('当前没有任何计分记录，确定要结束对局吗？', () => {
                    Navigation.goTo('index.html');
                });
                return;
            }

            // 创建对局总结
            const gameSession = LocalStorage.getItem('currentGameSession');
            const sessionSummary = {
                id: gameSession?.id || Utils.generateId(),
                startTime: gameSession?.startTime || new Date().toISOString(),
                endTime: new Date().toISOString(),
                players: [...gameData.players],
                settings: { ...gameData.settings },
                totalRounds: gameData.roundCounter - 1,
                finalScores: { ...gameData.totalScores },
                scoringHistory: [...gameData.scoringHistory],
                gameMode: gameData.settings.gameMode || 'classic'
            };

            // 保存对局总结到历史记录
            const gameHistories = LocalStorage.getItem('gameHistories') || [];
            gameHistories.unshift(sessionSummary);

            // 只保留最近50场对局
            if (gameHistories.length > 50) {
                gameHistories.splice(50);
            }

            LocalStorage.setItem('gameHistories', gameHistories);

            // 清除当前游戏数据
            LocalStorage.removeItem('currentGameSession');
            LocalStorage.removeItem('currentGameSettings');
            LocalStorage.removeItem('currentGamePlayers');

            Utils.showToast('对局已结束，数据已保存到历史记录', 'success');

            // 跳转到首页
            setTimeout(() => {
                Navigation.goTo('index.html');
            }, 1500);
        }

        function validatePlayerData() {
            // 检查当前游戏中的玩家是否仍然存在
            const allPlayers = PlayerManager.getPlayers();
            const validPlayerIds = new Set(allPlayers.map(p => p.id));

            let hasInvalidPlayers = false;
            gameData.players.forEach(playerId => {
                if (!validPlayerIds.has(playerId)) {
                    hasInvalidPlayers = true;
                }
            });

            if (hasInvalidPlayers) {
                Utils.showToast('检测到玩家数据变更，请重新设置游戏', 'warning');
                setTimeout(() => {
                    Navigation.goTo('game-setup.html');
                }, 2000);
            }
        }
    </script>
