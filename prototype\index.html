<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>斗地主计分助手</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="phone-container">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="status-time">14:30</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <div></div>
                <div class="nav-title">斗地主计分助手</div>
                <button class="nav-button" onclick="Navigation.goTo('settings.html')">
                    <i class="fas fa-cog"></i>
                </button>
            </div>

            <!-- 主内容 -->
            <div class="content">
                <!-- 欢迎卡片 -->
                <div class="card fade-in">
                    <div class="welcome-section">
                        <div class="welcome-icons">
                            <span style="color: #DC143C;">♠</span>
                            <span style="color: #FF0000;">♥</span>
                            <span style="color: #000;">♣</span>
                            <span style="color: #FF0000;">♦</span>
                        </div>
                        <h2 class="welcome-title">斗地主计分助手</h2>
                        <p class="welcome-subtitle">公平、准确、便捷的计分工具</p>
                    </div>
                </div>

                <!-- 快速开始 -->
                <div class="card fade-in">
                    <div class="card-title">
                        <i class="fas fa-play-circle" style="color: #DC143C; margin-right: 8px;"></i>
                        快速开始
                    </div>
                    <button class="btn btn-primary btn-large btn-full" onclick="Navigation.goTo('game-setup.html')">
                        <i class="fas fa-gamepad" style="margin-right: 8px;"></i>
                        开始新游戏
                    </button>
                </div>

                <!-- 统计概览 -->
                <div class="card fade-in">
                    <div class="card-title">
                        <i class="fas fa-chart-bar" style="color: #28A745; margin-right: 8px;"></i>
                        今日统计
                    </div>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value" style="color: #DC143C;" id="todayGames">0</div>
                            <div class="stat-label">今日局数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" style="color: #28A745;" id="todayWins">0</div>
                            <div class="stat-label">获胜次数</div>
                        </div>
                    </div>
                </div>

                <!-- 功能菜单 -->
                <div class="card fade-in">
                    <div class="card-title">
                        <i class="fas fa-th-large" style="color: #007AFF; margin-right: 8px;"></i>
                        功能菜单
                    </div>
                    <div class="menu-grid">
                        <button class="menu-item" onclick="Navigation.goTo('players.html')">
                            <i class="fas fa-users"></i>
                            <span>玩家管理</span>
                        </button>
                        <button class="menu-item" onclick="Navigation.goTo('history.html')">
                            <i class="fas fa-history"></i>
                            <span>游戏记录</span>
                        </button>
                        <button class="menu-item" onclick="Navigation.goTo('statistics.html')">
                            <i class="fas fa-chart-pie"></i>
                            <span>统计分析</span>
                        </button>
                        <button class="menu-item" onclick="Navigation.goTo('settings.html')">
                            <i class="fas fa-cog"></i>
                            <span>游戏设置</span>
                        </button>
                    </div>
                </div>

                <!-- 最近游戏 -->
                <div class="card fade-in">
                    <div class="card-title">
                        <i class="fas fa-clock" style="color: #FF6B35; margin-right: 8px;"></i>
                        最近游戏
                    </div>
                    <div id="recentGames">
                        <div class="empty-state">
                            <i class="fas fa-inbox"></i>
                            <p>暂无游戏记录</p>
                            <p class="subtitle">开始第一局游戏吧！</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator">
                <div class="home-bar"></div>
            </div>
        </div>
    </div>

    <style>
        .menu-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .menu-item {
            background: linear-gradient(135deg, #F8F9FA, #E9ECEF);
            border: none;
            border-radius: 12px;
            padding: 20px 16px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            transition: all 0.2s;
            color: #333;
            font-weight: 600;
        }

        .menu-item:hover {
            background: linear-gradient(135deg, #E9ECEF, #DEE2E6);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .menu-item i {
            font-size: 24px;
            color: #DC143C;
        }

        .menu-item span {
            font-size: 14px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            text-align: center;
        }

        .stat-item {
            padding: 16px;
            background: linear-gradient(135deg, #FFF8DC, #FFFACD);
            border-radius: 10px;
        }

        .stat-value {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
        }

        .welcome-section {
            text-align: center;
            padding: 24px 0;
        }

        .welcome-icons {
            font-size: 52px;
            margin-bottom: 20px;
            letter-spacing: 8px;
        }

        .welcome-title {
            color: #DC143C;
            margin-bottom: 8px;
            font-size: 24px;
            font-weight: 700;
        }

        .welcome-subtitle {
            color: #666;
            font-size: 14px;
        }

        .empty-state {
            text-align: center;
            padding: 32px 20px;
            color: #666;
        }

        .empty-state i {
            font-size: 36px;
            margin-bottom: 12px;
            opacity: 0.5;
        }

        .empty-state p {
            margin-bottom: 4px;
        }

        .empty-state .subtitle {
            font-size: 12px;
            opacity: 0.7;
        }
    </style>

    <script src="js/common.js"></script>
    <script>
        // 页面加载完成后更新统计数据
        document.addEventListener('DOMContentLoaded', function() {
            updateTodayStats();
            loadRecentGames();

            // 监听数据更新事件，实现实时同步
            window.addEventListener('gameDataUpdated', function() {
                updateTodayStats();
                loadRecentGames();
            });

            window.addEventListener('playerDataUpdated', function() {
                updateTodayStats();
                loadRecentGames();
            });
        });

        function updateTodayStats() {
            const records = GameRecordManager.getRecords();
            const today = new Date().toDateString();
            const todayRecords = records.filter(record =>
                new Date(record.createTime).toDateString() === today
            );

            document.getElementById('todayGames').textContent = todayRecords.length;

            // 计算今日获胜次数（当前用户参与的获胜游戏）
            const currentUser = getCurrentUser();
            let todayWins = 0;

            if (currentUser) {
                todayWins = todayRecords.filter(record => {
                    const playerScore = record.scores.find(s => s.playerId === currentUser.id);
                    return playerScore && playerScore.score > 0;
                }).length;
            } else {
                // 如果没有当前用户概念，显示地主获胜次数
                todayWins = todayRecords.filter(record => record.isLandlordWin).length;
            }

            document.getElementById('todayWins').textContent = todayWins;
        }

        function getCurrentUser() {
            // 简化实现：返回第一个玩家作为当前用户
            const players = PlayerManager.getPlayers();
            return players.length > 0 ? players[0] : null;
        }

        function loadRecentGames() {
            const records = GameRecordManager.getRecords();
            const recentGamesContainer = document.getElementById('recentGames');

            if (records.length === 0) {
                return; // 保持默认的空状态显示
            }

            const recentRecords = records.slice(0, 5); // 显示最近5局
            const players = PlayerManager.getPlayers();

            recentGamesContainer.innerHTML = recentRecords.map((record, index) => {
                const landlordPlayer = players.find(p => p.id === record.landlord);
                const gameTime = Utils.formatDate(record.createTime);
                const winnerText = record.isLandlordWin ? '地主胜' : '农民胜';
                const winnerColor = record.isLandlordWin ? '#FFD700' : '#28A745';

                return `
                    <div class="list-item" onclick="showGameDetail('${record.id}')" style="cursor: pointer;">
                        <div class="avatar" style="background: ${getPlayerAvatarColor(landlordPlayer)};">
                            <i class="fas ${getPlayerAvatarIcon(landlordPlayer)}"></i>
                        </div>
                        <div style="flex: 1;">
                            <div style="display: flex; align-items: center; margin-bottom: 6px;">
                                <span style="font-weight: 600; margin-right: 8px;">第${records.length - index}局</span>
                                <span class="game-result ${record.isLandlordWin ? 'landlord-win' : 'farmer-win'}">${winnerText}</span>
                            </div>
                            <div style="font-size: 13px; color: #666;">
                                地主: ${landlordPlayer ? landlordPlayer.name : '未知'} • ${gameTime}
                            </div>
                        </div>
                        <div style="text-align: right;">
                            <div style="font-size: 16px; font-weight: 600; color: #DC143C;">×${record.multiplier}</div>
                            <div style="font-size: 12px; color: #666;">${record.baseScore}分底</div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function getPlayerAvatarColor(player) {
            if (!player) return '#E0E0E0';
            const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'];
            const index = player.name.charCodeAt(0) % colors.length;
            return colors[index];
        }

        function getPlayerAvatarIcon(player) {
            if (!player) return 'fa-user';
            return player.avatar || 'fa-user';
        }

        function showGameDetail(gameId) {
            // 跳转到历史记录页面并显示详情
            localStorage.setItem('showGameDetail', gameId);
            Navigation.goTo('history.html');
        }
    </script>
</body>
</html>
