<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统计分析 - 斗地主计分助手</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="phone-container">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="status-time">14:30</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <button class="nav-button back" onclick="Navigation.goTo('index.html')">
                    <i class="fas fa-chevron-left"></i> 返回
                </button>
                <div class="nav-title">统计分析</div>
                <button class="nav-button" onclick="refreshStatistics()">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>

            <!-- 主内容 -->
            <div class="content">
                <!-- 总体统计 -->
                <div class="card fade-in">
                    <div class="card-title">
                        <i class="fas fa-chart-pie" style="color: #007AFF; margin-right: 8px;"></i>
                        总体统计
                    </div>
                    <div class="overall-stats">
                        <div class="stat-circle">
                            <div class="circle-progress" id="winRateCircle">
                                <div class="circle-inner">
                                    <span id="overallWinRate">0%</span>
                                    <small>总胜率</small>
                                </div>
                            </div>
                        </div>
                        <div class="stat-details">
                            <div class="stat-row">
                                <span class="stat-label">总游戏局数:</span>
                                <span id="totalGames">0</span>
                            </div>
                            <div class="stat-row">
                                <span class="stat-label">总获胜局数:</span>
                                <span id="totalWins">0</span>
                            </div>
                            <div class="stat-row">
                                <span class="stat-label">累计得分:</span>
                                <span id="totalScore" class="score-value">0</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 角色统计 -->
                <div class="card fade-in">
                    <div class="card-title">
                        <i class="fas fa-user-friends" style="color: #28A745; margin-right: 8px;"></i>
                        角色统计
                    </div>
                    <div class="role-stats">
                        <div class="role-stat-item">
                            <div class="role-icon landlord">
                                <i class="fas fa-crown"></i>
                            </div>
                            <div class="role-info">
                                <div class="role-title">地主统计</div>
                                <div class="role-details">
                                    <div>局数: <span id="landlordGames">0</span></div>
                                    <div>胜率: <span id="landlordWinRate">0%</span></div>
                                </div>
                            </div>
                        </div>
                        <div class="role-stat-item">
                            <div class="role-icon farmer">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="role-info">
                                <div class="role-title">农民统计</div>
                                <div class="role-details">
                                    <div>局数: <span id="farmerGames">0</span></div>
                                    <div>胜率: <span id="farmerWinRate">0%</span></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 玩家排行榜 -->
                <div class="card fade-in">
                    <div class="card-title">
                        <i class="fas fa-trophy" style="color: #FFA500; margin-right: 8px;"></i>
                        玩家排行榜
                    </div>
                    <div id="playerRankings">
                        <!-- 玩家排行榜将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 时间筛选 -->
                <div class="card fade-in">
                    <div class="card-title">
                        <i class="fas fa-calendar" style="color: #6C757D; margin-right: 8px;"></i>
                        时间筛选
                    </div>
                    <div class="time-filter-buttons">
                        <button class="time-filter-btn active" data-period="all" onclick="filterByPeriod('all')">
                            全部
                        </button>
                        <button class="time-filter-btn" data-period="month" onclick="filterByPeriod('month')">
                            本月
                        </button>
                        <button class="time-filter-btn" data-period="year" onclick="filterByPeriod('year')">
                            本年
                        </button>
                        <button class="time-filter-btn" data-period="week" onclick="filterByPeriod('week')">
                            本周
                        </button>
                    </div>
                </div>

                <!-- 游戏趋势 -->
                <div class="card fade-in">
                    <div class="card-title">
                        <i class="fas fa-chart-line" style="color: #DC143C; margin-right: 8px;"></i>
                        游戏趋势
                        <span class="period-label" id="periodLabel">全部时间</span>
                    </div>
                    <div class="trend-stats">
                        <div class="trend-item">
                            <div class="trend-label">最高单局得分</div>
                            <div class="trend-value" id="maxScore">0</div>
                        </div>
                        <div class="trend-item">
                            <div class="trend-label">最高倍数</div>
                            <div class="trend-value" id="maxMultiplier">0</div>
                        </div>
                        <div class="trend-item">
                            <div class="trend-label">平均倍数</div>
                            <div class="trend-value" id="avgMultiplier">0</div>
                        </div>
                        <div class="trend-item">
                            <div class="trend-label">连胜记录</div>
                            <div class="trend-value" id="maxWinStreak">0</div>
                        </div>
                    </div>
                </div>



                <!-- 详细分析 -->
                <div class="card fade-in">
                    <div class="card-title">
                        <i class="fas fa-analytics" style="color: #6F42C1; margin-right: 8px;"></i>
                        详细分析
                    </div>
                    <div class="detailed-analysis">
                        <div class="analysis-item">
                            <div class="analysis-label">平均每局得分</div>
                            <div class="analysis-value" id="avgScorePerGame">0</div>
                        </div>
                        <div class="analysis-item">
                            <div class="analysis-label">最常用倍数</div>
                            <div class="analysis-value" id="mostUsedMultiplier">1倍</div>
                        </div>
                        <div class="analysis-item">
                            <div class="analysis-label">炸弹使用率</div>
                            <div class="analysis-value" id="bombUsageRate">0%</div>
                        </div>
                        <div class="analysis-item">
                            <div class="analysis-label">春天/反春天率</div>
                            <div class="analysis-value" id="springRate">0%</div>
                        </div>
                    </div>
                </div>

                <!-- 最近表现 & 游戏习惯 -->
                <div class="card fade-in">
                    <div class="card-title">
                        <i class="fas fa-clock" style="color: #E91E63; margin-right: 8px;"></i>
                        最近表现
                        <span class="subtitle">最近10局</span>
                    </div>
                    <div class="recent-performance" id="recentPerformance">
                        <!-- 最近表现将通过JavaScript动态生成 -->
                    </div>
                </div>


            </div>

            <!-- Home Indicator -->
            <div class="home-indicator">
                <div class="home-bar"></div>
            </div>
        </div>
    </div>

    <style>
        .overall-stats {
            display: flex;
            align-items: center;
            gap: 32px;
            padding: 16px;
            background: linear-gradient(135deg, #F8F9FA, #E9ECEF);
            border-radius: 12px;
        }

        .stat-circle {
            flex-shrink: 0;
        }

        .circle-progress {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: conic-gradient(#DC143C 0deg, #DC143C calc(var(--progress, 0) * 1deg), #E0E0E0 calc(var(--progress, 0) * 1deg));
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .circle-inner {
            width: 90px;
            height: 90px;
            background: white;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }

        .circle-inner span {
            font-size: 22px;
            font-weight: 700;
            color: #DC143C;
        }

        .circle-inner small {
            font-size: 11px;
            color: #666;
            font-weight: 600;
            margin-top: 2px;
        }

        .stat-details {
            flex: 1;
        }

        .stat-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #F0F0F0;
        }

        .stat-row:last-child {
            border-bottom: none;
        }

        .stat-label {
            font-size: 15px;
            color: #666;
            font-weight: 600;
        }

        .score-value {
            font-weight: 700;
            color: #DC143C;
            font-size: 16px;
        }

        .role-stats {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .role-stat-item {
            display: flex;
            align-items: center;
            padding: 16px;
            background: linear-gradient(135deg, #F8F9FA, #E9ECEF);
            border-radius: 10px;
        }

        .role-icon {
            width: 50px;
            height: 50px;
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin-right: 16px;
        }

        .role-icon.landlord {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #8B0000;
        }

        .role-icon.farmer {
            background: linear-gradient(135deg, #228B22, #32CD32);
            color: white;
        }

        .role-info {
            flex: 1;
        }

        .role-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .role-details {
            display: flex;
            gap: 16px;
            font-size: 14px;
            color: #666;
        }

        .ranking-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            margin-bottom: 8px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 1px 4px rgba(0,0,0,0.08);
            transition: transform 0.2s ease;
        }

        .ranking-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.12);
        }

        .ranking-item:last-child {
            margin-bottom: 0;
        }

        .ranking-item.top-player {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: white;
            margin-bottom: 12px;
        }

        .rank-number {
            width: 28px;
            height: 28px;
            border-radius: 14px;
            background: #E9ECEF;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 700;
            margin-right: 12px;
            color: #495057;
            flex-shrink: 0;
        }

        .top-player .rank-number {
            background: rgba(255,255,255,0.3);
            color: white;
        }

        .player-avatar {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: white;
            font-size: 14px;
            flex-shrink: 0;
        }

        .player-details {
            flex: 1;
            min-width: 0;
        }

        .player-name {
            font-size: 15px;
            font-weight: 600;
            margin-bottom: 4px;
            color: #333;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .top-player .player-name {
            color: white;
        }

        .player-stats {
            display: flex;
            gap: 12px;
            margin-bottom: 4px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 3px;
            font-size: 11px;
            color: #666;
        }

        .top-player .stat-item {
            color: rgba(255,255,255,0.9);
        }

        .role-stats {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .role-stat {
            display: flex;
            align-items: center;
            gap: 3px;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 8px;
            background: rgba(0,0,0,0.1);
            white-space: nowrap;
        }

        .role-stat.landlord {
            background: rgba(255,215,0,0.2);
            color: #B8860B;
        }

        .role-stat.farmer {
            background: rgba(40,167,69,0.2);
            color: #28A745;
        }

        .top-player .role-stat {
            background: rgba(255,255,255,0.2);
            color: white;
        }

        .score-info {
            text-align: right;
            flex-shrink: 0;
            margin-left: 8px;
        }

        .total-score {
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 2px;
        }

        .avg-score {
            font-size: 10px;
            color: #666;
        }

        .top-player .avg-score {
            color: rgba(255,255,255,0.8);
        }

        .player-stats {
            flex: 1;
            margin-left: 12px;
        }

        .player-name {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .player-details {
            display: flex;
            gap: 12px;
            font-size: 12px;
            color: #666;
        }

        .total-score {
            font-size: 18px;
            font-weight: 600;
        }

        .total-score.positive {
            color: #28A745;
        }

        .total-score.negative {
            color: #DC3545;
        }

        .trend-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        .trend-item {
            text-align: center;
            padding: 16px;
            background: linear-gradient(135deg, #F8F9FA, #E9ECEF);
            border-radius: 8px;
        }

        .trend-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
        }

        .trend-value {
            font-size: 20px;
            font-weight: 600;
            color: #DC143C;
        }

        .time-filter-buttons {
            display: flex;
            gap: 8px;
            padding: 16px;
            background: #F8F9FA;
            border-radius: 12px;
            flex-wrap: wrap;
        }

        .time-filter-btn {
            flex: 1;
            min-width: 60px;
            padding: 12px 16px;
            border: 2px solid #E0E0E0;
            border-radius: 8px;
            background: white;
            color: #6C757D;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .time-filter-btn:hover {
            border-color: #DC143C;
            color: #DC143C;
            transform: translateY(-2px);
        }

        .time-filter-btn.active {
            background: #DC143C;
            border-color: #DC143C;
            color: white;
            box-shadow: 0 4px 12px rgba(220, 20, 60, 0.3);
        }

        .period-label {
            font-size: 12px;
            color: #6C757D;
            margin-left: 8px;
            padding: 4px 8px;
            background: #F8F9FA;
            border-radius: 4px;
        }



        .detailed-analysis {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 16px;
            padding: 16px;
            background: linear-gradient(135deg, #F8F9FA, #E9ECEF);
            border-radius: 12px;
        }

        .analysis-item {
            text-align: center;
            padding: 16px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .analysis-item:hover {
            transform: translateY(-2px);
        }

        .analysis-label {
            font-size: 12px;
            color: #6C757D;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .analysis-value {
            font-size: 18px;
            font-weight: 700;
            color: #DC143C;
        }

        .subtitle {
            font-size: 12px;
            color: #6C757D;
            margin-left: 8px;
            padding: 2px 8px;
            background: #F8F9FA;
            border-radius: 12px;
            font-weight: 500;
        }

        .recent-performance {
            padding: 16px;
            background: #F8F9FA;
            border-radius: 12px;
        }

        .performance-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 8px;
            margin-bottom: 16px;
        }

        .performance-dot {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            color: white;
        }

        .performance-dot.win {
            background: #28A745;
        }

        .performance-dot.lose {
            background: #DC3545;
        }

        .performance-summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 12px;
            border-top: 1px solid #E9ECEF;
        }

        .performance-stat {
            text-align: center;
        }

        .performance-stat-value {
            font-size: 18px;
            font-weight: 700;
            color: #DC143C;
        }

        .performance-stat-label {
            font-size: 12px;
            color: #6C757D;
            margin-top: 4px;
        }


    </style>

    <script src="js/common.js"></script>
    <script>
        let currentPeriod = 'all';

        // 页面加载完成后加载统计数据
        document.addEventListener('DOMContentLoaded', function() {
            loadStatistics();

            // 监听数据更新事件，实现实时同步
            window.addEventListener('gameDataUpdated', function() {
                loadStatistics();
            });

            window.addEventListener('playerDataUpdated', function() {
                loadStatistics(); // 重新加载以更新玩家信息
            });
        });

        function loadStatistics() {
            const records = getFilteredRecords();
            const players = PlayerManager.getPlayers();

            if (records.length === 0) {
                showEmptyState();
                return;
            }

            // 重新计算统计数据
            const statistics = calculatePlayerStatistics(records, players);

            // 计算总体统计
            calculateOverallStats(records, statistics);

            // 计算角色统计
            calculateRoleStats(records, statistics);

            // 生成玩家排行榜
            generatePlayerRankings(statistics, players);

            // 计算游戏趋势
            calculateGameTrends(records);

            // 更新详细分析
            updateDetailedAnalysis();

            // 更新最近表现
            updateRecentPerformance(records);
        }

        function calculatePlayerStatistics(records, players) {
            const statistics = {};

            // 初始化每个玩家的统计数据
            players.forEach(player => {
                statistics[player.id] = {
                    playerId: player.id,
                    playerName: player.name,
                    totalGames: 0,
                    winGames: 0,
                    loseGames: 0,
                    totalScore: 0,
                    landlordGames: 0,
                    landlordWins: 0,
                    farmerGames: 0,
                    farmerWins: 0,
                    maxScore: 0,
                    minScore: 0,
                    avgScore: 0
                };
            });

            // 遍历所有游戏记录计算统计
            records.forEach(record => {
                if (!record.scores) return;

                record.scores.forEach(scoreData => {
                    const playerId = scoreData.playerId;
                    if (!statistics[playerId]) return;

                    const stats = statistics[playerId];
                    const isLandlord = playerId === record.landlord;
                    const isWin = scoreData.score > 0;

                    stats.totalGames++;
                    stats.totalScore += scoreData.score;

                    if (isWin) stats.winGames++;
                    else stats.loseGames++;

                    if (isLandlord) {
                        stats.landlordGames++;
                        if (isWin) stats.landlordWins++;
                    } else {
                        stats.farmerGames++;
                        if (isWin) stats.farmerWins++;
                    }

                    if (scoreData.score > stats.maxScore) stats.maxScore = scoreData.score;
                    if (scoreData.score < stats.minScore) stats.minScore = scoreData.score;
                });
            });

            // 计算平均分
            Object.values(statistics).forEach(stats => {
                if (stats.totalGames > 0) {
                    stats.avgScore = Math.round(stats.totalScore / stats.totalGames);
                }
            });

            return statistics;
        }

        function calculateOverallStats(records, statistics) {
            const totalGames = records.length;
            let totalWins = 0;
            let totalScore = 0;
            let totalPlayers = 0;

            // 计算总胜利次数和总得分
            Object.values(statistics).forEach(playerStats => {
                if (playerStats.totalGames > 0) {
                    totalWins += playerStats.winGames;
                    totalScore += playerStats.totalScore;
                    totalPlayers++;
                }
            });

            const avgWinRate = totalPlayers > 0 && totalGames > 0 ?
                Math.round((totalWins / (totalGames * 3)) * 100) : 0;

            document.getElementById('totalGames').textContent = totalGames;
            document.getElementById('totalWins').textContent = totalWins;
            document.getElementById('totalScore').textContent = totalScore;
            document.getElementById('overallWinRate').textContent = avgWinRate + '%';

            // 更新胜率圆环
            updateWinRateCircle(avgWinRate);
        }

        function calculateRoleStats(records, statistics) {
            let landlordGames = 0;
            let landlordWins = 0;
            let farmerGames = 0;
            let farmerWins = 0;

            Object.values(statistics).forEach(playerStats => {
                landlordGames += playerStats.landlordGames;
                landlordWins += playerStats.landlordWins;
                farmerGames += (playerStats.totalGames - playerStats.landlordGames);
                farmerWins += (playerStats.winGames - playerStats.landlordWins);
            });

            const landlordWinRate = landlordGames > 0 ? Math.round((landlordWins / landlordGames) * 100) : 0;
            const farmerWinRate = farmerGames > 0 ? Math.round((farmerWins / farmerGames) * 100) : 0;

            document.getElementById('landlordGames').textContent = landlordGames;
            document.getElementById('landlordWinRate').textContent = landlordWinRate + '%';
            document.getElementById('farmerGames').textContent = farmerGames;
            document.getElementById('farmerWinRate').textContent = farmerWinRate + '%';
        }

        function updateRoleProgress(elementId, percentage) {
            const progressBar = document.getElementById(elementId);
            if (progressBar) {
                progressBar.style.width = percentage + '%';
            }
        }

        function updateWinRateCircle(winRate) {
            const circle = document.getElementById('winRateCircle');
            circle.style.setProperty('--progress', winRate * 3.6); // 360度对应100%
        }

        function generatePlayerRankings(statistics, players) {
            const playerRankingsContainer = document.getElementById('playerRankings');

            // 按总得分排序玩家
            const rankedPlayers = Object.values(statistics)
                .filter(stats => stats.totalGames > 0)
                .sort((a, b) => b.totalScore - a.totalScore);

            if (rankedPlayers.length === 0) {
                playerRankingsContainer.innerHTML = `
                    <div class="empty-rankings">
                        <i class="fas fa-chart-bar"></i>
                        <p>暂无玩家数据</p>
                        <p class="subtitle">开始游戏后将显示排行榜</p>
                    </div>
                `;
                return;
            }

            playerRankingsContainer.innerHTML = rankedPlayers.map((playerData, index) => {
                const winRate = playerData.totalGames > 0 ?
                    Math.round((playerData.winGames / playerData.totalGames) * 100) : 0;
                const isTopPlayer = index === 0;

                return `
                    <div class="ranking-item ${isTopPlayer ? 'top-player' : ''}">
                        <div class="rank-number">
                            ${isTopPlayer ? '<i class="fas fa-crown"></i>' : `#${index + 1}`}
                        </div>
                        <div class="player-avatar" style="background: ${getPlayerAvatarColor(playerData.playerName)};">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="player-details">
                            <div class="player-name">${playerData.playerName}</div>
                            <div class="player-stats">
                                <span class="stat-item">
                                    <i class="fas fa-gamepad"></i>
                                    ${playerData.totalGames}局
                                </span>
                                <span class="stat-item">
                                    <i class="fas fa-percentage"></i>
                                    ${winRate}%
                                </span>
                            </div>
                        </div>
                        <div class="score-info">
                            <div class="total-score ${playerData.totalScore >= 0 ? 'positive' : 'negative'}">
                                ${playerData.totalScore >= 0 ? '+' : ''}${playerData.totalScore}
                            </div>
                            <div class="avg-score">
                                平均${playerData.avgScore >= 0 ? '+' : ''}${playerData.avgScore}
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function getPlayerAvatarColor(playerName) {
            const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#F8B500', '#FF8C94'];
            const index = playerName.charCodeAt(0) % colors.length;
            return colors[index];
        }

        function calculateGameTrends(records) {
            let maxScore = 0;
            let maxMultiplier = 0;
            let totalMultiplier = 0;
            let maxWinStreak = 0;
            let currentWinStreak = 0;
            let lastWinner = null;

            records.forEach(record => {
                // 最高单局得分
                Object.values(record.finalScores).forEach(score => {
                    if (Math.abs(score) > Math.abs(maxScore)) {
                        maxScore = score;
                    }
                });

                // 最高倍数
                if (record.multiplier > maxMultiplier) {
                    maxMultiplier = record.multiplier;
                }

                // 累计倍数
                totalMultiplier += record.multiplier;

                // 连胜记录（简化为地主连胜）
                const currentWinner = record.isLandlordWin ? 'landlord' : 'farmer';
                if (currentWinner === lastWinner) {
                    currentWinStreak++;
                } else {
                    if (currentWinStreak > maxWinStreak) {
                        maxWinStreak = currentWinStreak;
                    }
                    currentWinStreak = 1;
                    lastWinner = currentWinner;
                }
            });

            // 检查最后的连胜
            if (currentWinStreak > maxWinStreak) {
                maxWinStreak = currentWinStreak;
            }

            const avgMultiplier = records.length > 0 ? (totalMultiplier / records.length).toFixed(1) : 0;

            document.getElementById('maxScore').textContent = maxScore;
            document.getElementById('maxMultiplier').textContent = maxMultiplier;
            document.getElementById('avgMultiplier').textContent = avgMultiplier;
            document.getElementById('maxWinStreak').textContent = maxWinStreak;
        }

        function getAvatarIcon(avatarType) {
            switch(avatarType) {
                case 'landlord':
                    return '<i class="fas fa-crown" style="color: #FFD700; font-size: 20px;"></i>';
                case 'farmer':
                    return '<i class="fas fa-user" style="color: #28A745; font-size: 20px;"></i>';
                default:
                    return '<i class="fas fa-user-circle" style="color: #6C757D; font-size: 20px;"></i>';
            }
        }

        function showEmptyState() {
            document.querySelector('.content').innerHTML = `
                <div class="card fade-in" style="text-align: center; padding: 40px 20px;">
                    <i class="fas fa-chart-bar" style="font-size: 48px; color: #DDD; margin-bottom: 16px;"></i>
                    <h3 style="color: #666; margin-bottom: 8px;">暂无统计数据</h3>
                    <p style="color: #999; font-size: 14px; margin-bottom: 20px;">开始游戏后即可查看详细统计</p>
                    <button class="btn btn-primary" onclick="Navigation.goTo('game-setup.html')">
                        <i class="fas fa-play" style="margin-right: 8px;"></i>
                        开始游戏
                    </button>
                </div>
            `;
        }

        function filterByPeriod(period) {
            currentPeriod = period;

            // 更新按钮状态
            document.querySelectorAll('.time-filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-period="${period}"]`).classList.add('active');

            // 更新期间标签
            const periodLabels = {
                'all': '全部时间',
                'month': '本月',
                'year': '本年',
                'week': '本周'
            };
            document.getElementById('periodLabel').textContent = periodLabels[period];

            // 重新加载统计数据
            loadStatistics();
        }

        function getFilteredRecords() {
            const allRecords = GameRecordManager.getRecords();
            const now = new Date();

            switch (currentPeriod) {
                case 'week':
                    const weekStart = new Date(now);
                    weekStart.setDate(now.getDate() - now.getDay());
                    weekStart.setHours(0, 0, 0, 0);
                    return allRecords.filter(record => new Date(record.createTime) >= weekStart);

                case 'month':
                    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
                    return allRecords.filter(record => new Date(record.createTime) >= monthStart);

                case 'year':
                    const yearStart = new Date(now.getFullYear(), 0, 1);
                    return allRecords.filter(record => new Date(record.createTime) >= yearStart);

                default:
                    return allRecords;
            }
        }



        function updateDetailedAnalysis() {
            const records = getFilteredRecords();

            if (records.length === 0) {
                document.getElementById('avgScorePerGame').textContent = '0';
                document.getElementById('mostUsedMultiplier').textContent = '1倍';
                document.getElementById('bombUsageRate').textContent = '0%';
                document.getElementById('springRate').textContent = '0%';
                return;
            }

            // 计算平均每局得分
            const totalScore = records.reduce((sum, record) => {
                return sum + record.scores.reduce((scoreSum, score) => scoreSum + Math.abs(score.score), 0);
            }, 0);
            const avgScore = Math.round(totalScore / records.length);
            document.getElementById('avgScorePerGame').textContent = avgScore;

            // 计算最常用倍数
            const multiplierCount = {};
            records.forEach(record => {
                const multiplier = record.multiplier;
                multiplierCount[multiplier] = (multiplierCount[multiplier] || 0) + 1;
            });
            const mostUsed = Object.keys(multiplierCount).reduce((a, b) =>
                multiplierCount[a] > multiplierCount[b] ? a : b, '1');
            document.getElementById('mostUsedMultiplier').textContent = mostUsed + '倍';

            // 计算炸弹使用率
            const bombGames = records.filter(record => record.bombCount > 0).length;
            const bombRate = Math.round((bombGames / records.length) * 100);
            document.getElementById('bombUsageRate').textContent = bombRate + '%';

            // 计算春天/反春天率
            const springGames = records.filter(record => record.isSpring || record.isAntiSpring).length;
            const springRate = Math.round((springGames / records.length) * 100);
            document.getElementById('springRate').textContent = springRate + '%';
        }

        function updateRecentPerformance(records) {
            const recentRecords = records.slice(-10); // 最近10局
            const container = document.getElementById('recentPerformance');

            if (recentRecords.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 20px; color: #999;">
                        <i class="fas fa-chart-line" style="font-size: 24px; margin-bottom: 8px;"></i>
                        <p>暂无最近游戏记录</p>
                    </div>
                `;
                return;
            }

            // 计算最近表现统计
            let recentWins = 0;
            let recentScore = 0;
            let recentLandlordWins = 0;
            let recentLandlordGames = 0;

            const performanceDots = recentRecords.map((record, index) => {
                // 简化：假设第一个玩家的得分代表胜负
                const playerScore = record.scores && record.scores[0] ? record.scores[0].score : 0;
                const isWin = playerScore > 0;

                if (isWin) recentWins++;
                recentScore += playerScore;

                if (record.isLandlordWin !== undefined) {
                    recentLandlordGames++;
                    if (record.isLandlordWin) recentLandlordWins++;
                }

                return `
                    <div class="performance-dot ${isWin ? 'win' : 'lose'}" title="第${index + 1}局: ${isWin ? '胜' : '负'} (${playerScore}分)">
                        ${isWin ? 'W' : 'L'}
                    </div>
                `;
            }).join('');

            const recentWinRate = recentRecords.length > 0 ? Math.round((recentWins / recentRecords.length) * 100) : 0;
            const avgRecentScore = recentRecords.length > 0 ? Math.round(recentScore / recentRecords.length) : 0;

            container.innerHTML = `
                <div class="performance-grid">
                    ${performanceDots}
                </div>
                <div class="performance-summary">
                    <div class="performance-stat">
                        <div class="performance-stat-value">${recentWinRate}%</div>
                        <div class="performance-stat-label">胜率</div>
                    </div>
                    <div class="performance-stat">
                        <div class="performance-stat-value">${recentWins}/${recentRecords.length}</div>
                        <div class="performance-stat-label">胜负</div>
                    </div>
                    <div class="performance-stat">
                        <div class="performance-stat-value">${avgRecentScore >= 0 ? '+' : ''}${avgRecentScore}</div>
                        <div class="performance-stat-label">平均得分</div>
                    </div>
                </div>
            `;
        }



        function refreshStatistics() {
            // 显示加载动画
            const refreshBtn = document.querySelector('.nav-button i.fa-sync-alt');
            refreshBtn.classList.add('fa-spin');

            // 重新加载统计数据
            setTimeout(() => {
                loadStatistics();
                refreshBtn.classList.remove('fa-spin');
                Utils.showToast('统计数据已更新', 'success');
            }, 500);
        }
    </script>
