<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>历史记录 - 斗地主计分助手</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="phone-container">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="status-time">14:30</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <button class="nav-button back" onclick="console.log('返回按钮被点击'); Navigation.goTo('index.html')">
                    <i class="fas fa-chevron-left"></i> 返回
                </button>
                <div class="nav-title">历史记录</div>
                <button class="nav-button" onclick="clearAllHistory()">
                    <i class="fas fa-trash"></i>
                </button>
            </div>

            <!-- 主内容 -->
            <div class="content">
                <!-- 统计概览 -->
                <div class="card fade-in">
                    <div class="card-title">
                        <i class="fas fa-chart-bar" style="color: #007AFF; margin-right: 8px;"></i>
                        游戏统计
                    </div>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value" id="totalGames">0</div>
                            <div class="stat-label">总局数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="landlordWins">0</div>
                            <div class="stat-label">地主胜</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="farmerWins">0</div>
                            <div class="stat-label">农民胜</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="avgMultiplier">0</div>
                            <div class="stat-label">平均倍数</div>
                        </div>
                    </div>
                </div>

                <!-- 历史记录列表 -->
                <div class="card fade-in">
                    <div class="card-title">
                        <i class="fas fa-history" style="color: #28A745; margin-right: 8px;"></i>
                        游戏记录
                    </div>
                    <div id="historyList">
                        <!-- 历史记录将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator">
                <div class="home-bar"></div>
            </div>
        </div>
    </div>

    <!-- 记录详情模态框 -->
    <div id="recordModal" class="modal" style="display: none;">
        <div class="modal-overlay" onclick="hideRecordModal()"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3>游戏详情</h3>
                <button class="modal-close" onclick="hideRecordModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="recordDetails">
                <!-- 记录详情将通过JavaScript动态生成 -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hideRecordModal()">关闭</button>
                <button class="btn btn-danger" onclick="deleteCurrentRecord()">删除记录</button>
            </div>
        </div>
    </div>

    <script src="js/common.js"></script>
    <script>
        let currentRecord = null;

        // 页面加载完成后加载历史记录
        document.addEventListener('DOMContentLoaded', function() {
            loadHistoryStats();
            loadHistoryList();

            // 监听数据更新事件，实现实时同步
            window.addEventListener('gameDataUpdated', function() {
                loadHistoryStats();
                loadHistoryList();
            });

            window.addEventListener('playerDataUpdated', function() {
                loadHistoryList(); // 重新加载以更新玩家信息
            });
        });

        function loadHistoryStats() {
            const records = GameRecordManager.getRecords();
            
            const totalGames = records.length;
            const landlordWins = records.filter(r => r.isLandlordWin).length;
            const farmerWins = totalGames - landlordWins;
            const avgMultiplier = totalGames > 0 ? 
                (records.reduce((sum, r) => sum + r.multiplier, 0) / totalGames).toFixed(1) : 0;

            document.getElementById('totalGames').textContent = totalGames;
            document.getElementById('landlordWins').textContent = landlordWins;
            document.getElementById('farmerWins').textContent = farmerWins;
            document.getElementById('avgMultiplier').textContent = avgMultiplier;
        }

        function loadHistoryList() {
            const records = GameRecordManager.getRecords();
            const historyListContainer = document.getElementById('historyList');

            // 调试信息
            console.log('History.html - 加载记录数量:', records.length);
            console.log('History.html - 记录详情:', records);

            // 检查是否需要显示特定游戏详情
            const showGameDetail = localStorage.getItem('showGameDetail');
            if (showGameDetail) {
                localStorage.removeItem('showGameDetail');
                setTimeout(() => showRecordDetails(showGameDetail), 100);
            }

            if (records.length === 0) {
                historyListContainer.innerHTML = `
                    <div class="empty-history">
                        <i class="fas fa-inbox"></i>
                        <p>暂无游戏记录</p>
                        <p class="subtitle">开始第一局游戏吧！</p>
                        <button class="btn btn-primary" onclick="Navigation.goTo('game-setup.html')" style="margin-top: 16px;">
                            <i class="fas fa-gamepad" style="margin-right: 8px;"></i>
                            开始游戏
                        </button>
                        <button class="btn btn-secondary" onclick="createTestRecord()" style="margin-top: 8px;">
                            <i class="fas fa-plus" style="margin-right: 8px;"></i>
                            创建测试记录
                        </button>
                    </div>
                `;
                return;
            }

            const players = PlayerManager.getPlayers();

            historyListContainer.innerHTML = records.map((record, index) => {
                const landlordPlayer = players.find(p => p.id === record.landlord);
                const gameTime = Utils.formatDate(record.createTime);
                const gameNumber = records.length - index;

                // 计算特殊情况标签
                const specialTags = [];
                if (record.bombCount > 0) specialTags.push(`炸弹×${record.bombCount}`);
                if (record.isSpring) specialTags.push('春天');
                if (record.isAntiSpring) specialTags.push('反春天');

                return `
                    <div class="history-item" onclick="showRecordDetails('${record.id}')">
                        <div class="history-header">
                            <div class="game-info">
                                <span class="game-number">第${gameNumber}局</span>
                                <span class="game-date">${gameTime}</span>
                            </div>
                            <div class="game-result ${record.isLandlordWin ? 'landlord-win' : 'farmer-win'}">
                                ${record.isLandlordWin ? '地主胜' : '农民胜'}
                            </div>
                        </div>
                        <div class="history-details">
                            <div class="landlord-info">
                                <span class="landlord-badge">地主</span>
                                <span class="player-name">${landlordPlayer ? landlordPlayer.name : '已删除玩家'}</span>
                            </div>
                            <div class="game-stats">
                                <span class="multiplier">×${record.multiplier}</span>
                                <span class="base-score">${record.baseScore}分底</span>
                                ${specialTags.length > 0 ? `<span class="special-tags">${specialTags.join(' ')}</span>` : ''}
                            </div>
                        </div>
                        <div class="action-button">
                            <button class="btn-icon delete" onclick="event.stopPropagation(); confirmDeleteRecord('${record.id}', ${gameNumber})" title="删除记录">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function showRecordDetails(recordId) {
            console.log('点击了记录详情，ID:', recordId);
            const records = GameRecordManager.getRecords();
            console.log('所有记录:', records);
            const record = records.find(r => r.id === recordId);
            console.log('找到的记录:', record);
            if (!record) {
                Utils.showToast('游戏记录不存在', 'error');
                return;
            }

            currentRecord = record;
            const players = PlayerManager.getPlayers();

            const recordDetailsContainer = document.getElementById('recordDetails');

            // 按得分排序玩家
            const sortedScores = record.scores ? record.scores.sort((a, b) => b.score - a.score) : [];

            // 构建特殊情况信息
            const specialInfo = [];
            if (record.bombCount > 0) specialInfo.push(`炸弹 ×${record.bombCount}`);
            if (record.isSpring) specialInfo.push('春天');
            if (record.isAntiSpring) specialInfo.push('反春天');

            recordDetailsContainer.innerHTML = `
                <div class="record-info">
                    <div class="info-row">
                        <span class="info-label">游戏时间:</span>
                        <span>${Utils.formatDate(record.createTime)}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">游戏模式:</span>
                        <span>${record.gameMode === 'classic' ? '经典斗地主' : '癞子斗地主'}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">底分:</span>
                        <span>${record.baseScore}分</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">叫分倍数:</span>
                        <span>${record.callMultiplier}倍</span>
                    </div>
                    ${specialInfo.length > 0 ? `
                    <div class="info-row">
                        <span class="info-label">特殊情况:</span>
                        <span style="color: #FFA500; font-weight: 600;">${specialInfo.join(' • ')}</span>
                    </div>
                    ` : ''}
                    <div class="info-row">
                        <span class="info-label">最终倍数:</span>
                        <span style="color: #DC143C; font-weight: 600;">${record.multiplier}倍</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">游戏结果:</span>
                        <span class="${record.isLandlordWin ? 'landlord-win' : 'farmer-win'}">
                            ${record.isLandlordWin ? '地主获胜' : '农民获胜'}
                        </span>
                    </div>
                </div>

                <div class="players-scores">
                    <h4>玩家得分</h4>
                    ${sortedScores.map((scoreData, index) => {
                        const player = players.find(p => p.id === scoreData.playerId);
                        const isLandlord = scoreData.playerId === record.landlord;
                        const playerName = player ? player.name : '已删除玩家';

                        return `
                            <div class="player-score-row">
                                <div class="player-info">
                                    <span class="rank">#${index + 1}</span>
                                    <span class="player-name">${playerName}</span>
                                    ${isLandlord ? '<span class="landlord-badge">地主</span>' : '<span class="farmer-badge">农民</span>'}
                                </div>
                                <div class="score ${scoreData.score >= 0 ? 'positive' : 'negative'}">
                                    ${scoreData.score >= 0 ? '+' : ''}${scoreData.score}
                                </div>
                            </div>
                        `;
                    }).join('')}
                </div>
            `;

            document.getElementById('recordModal').style.display = 'flex';
        }

        function hideRecordModal() {
            document.getElementById('recordModal').style.display = 'none';
            currentRecord = null;
        }

        function confirmDeleteRecord(recordId, gameNumber) {
            const record = GameRecordManager.getRecords().find(r => r.id === recordId);
            if (!record) return;

            Utils.confirm(`确定要删除第${gameNumber}局游戏记录吗？\n\n删除后无法恢复。`, () => {
                deleteRecord(recordId);
            });
        }

        function deleteRecord(recordId) {
            try {
                GameRecordManager.deleteRecord(recordId);
                Utils.showToast('记录已删除', 'success');
                loadHistoryStats();
                loadHistoryList();
            } catch (error) {
                Utils.showToast('删除失败，请重试', 'error');
                console.error('Delete record error:', error);
            }
        }

        function deleteCurrentRecord() {
            if (!currentRecord) return;

            Utils.confirm('确定要删除这条游戏记录吗？\n\n删除后无法恢复。', () => {
                deleteRecord(currentRecord.id);
                hideRecordModal();
            });
        }

        function clearAllHistory() {
            const records = GameRecordManager.getRecords();
            if (records.length === 0) {
                Utils.showToast('没有记录可以清除', 'error');
                return;
            }

            Utils.confirm('确定要清除所有游戏记录吗？此操作不可恢复。', () => {
                LocalStorage.removeItem('gameRecords');
                LocalStorage.removeItem('statistics');

                Utils.showToast('所有记录已清除');
                loadHistoryStats();
                loadHistoryList();
            });
        }

        function createTestRecord() {
            // 确保有玩家数据
            let players = PlayerManager.getPlayers();
            if (players.length < 3) {
                // 创建测试玩家
                const testPlayers = [
                    { id: 'player1', name: '张三', avatar: 'landlord' },
                    { id: 'player2', name: '李四', avatar: 'farmer' },
                    { id: 'player3', name: '王五', avatar: 'farmer' }
                ];
                LocalStorage.setItem('players', testPlayers);
                players = testPlayers;
            }

            // 创建测试游戏记录
            const testRecord = {
                timestamp: new Date().toISOString(),
                gameDate: new Date().toISOString().split('T')[0],
                landlord: players[0].id,
                players: players.slice(0, 3).map(p => p.id),
                baseScore: 3,
                callMultiplier: 1,
                bombCount: 0,
                isSpring: false,
                isAntiSpring: false,
                isShowCard: false,
                multiplier: 1,
                isLandlordWin: true,
                gameMode: 'classic',
                scores: [
                    { playerId: players[0].id, score: 6, isLandlord: true },
                    { playerId: players[1].id, score: -3, isLandlord: false },
                    { playerId: players[2].id, score: -3, isLandlord: false }
                ],
                finalScores: {}
            };

            // 设置finalScores
            testRecord.scores.forEach(score => {
                testRecord.finalScores[score.playerId] = score.score;
            });

            try {
                GameRecordManager.addRecord(testRecord);
                Utils.showToast('测试记录已创建', 'success');
                loadHistoryStats();
                loadHistoryList();
            } catch (error) {
                console.error('创建测试记录失败:', error);
                Utils.showToast('创建失败: ' + error.message, 'error');
            }
        }
    </script>

    <style>
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        .stat-item {
            text-align: center;
            padding: 20px 16px;
            background: linear-gradient(135deg, #F8F9FA, #E9ECEF);
            border-radius: 12px;
            transition: transform 0.2s;
        }

        .stat-item:hover {
            transform: translateY(-2px);
        }

        .stat-value {
            font-size: 28px;
            font-weight: 700;
            color: #DC143C;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 13px;
            color: #666;
            font-weight: 600;
        }

        .history-item {
            padding: 16px;
            border-bottom: 1px solid #F0F0F0;
            cursor: pointer;
            transition: all 0.2s;
            border-radius: 8px;
            margin-bottom: 8px;
        }

        .history-item:hover {
            background: rgba(0,0,0,0.02);
            transform: translateX(4px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .history-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .history-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .game-date {
            font-size: 14px;
            color: #666;
        }

        .game-result {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .game-result.landlord-win {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #8B0000;
            box-shadow: 0 2px 4px rgba(255,215,0,0.3);
        }

        .game-result.farmer-win {
            background: linear-gradient(135deg, #228B22, #32CD32);
            color: white;
            box-shadow: 0 2px 4px rgba(34,139,34,0.3);
        }

        .history-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .game-stats {
            display: flex;
            gap: 12px;
            font-size: 12px;
            color: #666;
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            width: 90%;
            max-width: 400px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
            z-index: 1001;
            box-shadow: 0 8px 24px rgba(0,0,0,0.3);
        }

        .modal-header {
            padding: 20px 20px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 18px;
            color: #666;
            cursor: pointer;
            padding: 4px;
        }

        .modal-body {
            padding: 20px;
        }

        .modal-footer {
            padding: 0 20px 20px;
            display: flex;
            gap: 12px;
        }

        .modal-footer .btn {
            flex: 1;
        }

        .record-info {
            margin-bottom: 20px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #F0F0F0;
        }

        .info-row:last-child {
            border-bottom: none;
        }

        .info-label {
            font-size: 14px;
            color: #666;
        }

        .players-scores h4 {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .player-score-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #F0F0F0;
        }

        .player-score-row:last-child {
            border-bottom: none;
        }

        .player-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .player-name {
            font-size: 14px;
            font-weight: 600;
        }

        .score {
            font-size: 16px;
            font-weight: 600;
        }

        .score.positive {
            color: #28A745;
        }

        .score.negative {
            color: #DC3545;
        }

        .landlord-win {
            color: #8B0000;
        }

        .farmer-win {
            color: #228B22;
        }
    </style>
</body>
</html>
