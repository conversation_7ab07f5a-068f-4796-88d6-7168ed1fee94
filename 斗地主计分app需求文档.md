# 斗地主计分App需求文档

## 1. 项目概述

### 1.1 项目名称
斗地主计分助手

### 1.2 项目描述
一款专为斗地主爱好者设计的本地计分应用，支持多种游戏模式的积分记录、统计分析和历史查询功能。

### 1.3 目标用户
- 经常玩斗地主的朋友聚会群体
- 喜欢记录游戏数据的斗地主爱好者
- 需要公平计分系统的家庭娱乐用户

## 2. 用户细分

### 2.1 主要用户群体
**休闲玩家**
- 特征：偶尔玩斗地主，主要用于朋友聚会
- 需求：简单易用的计分功能，清晰的结算界面
- 使用场景：朋友聚会、家庭娱乐

**资深玩家**
- 特征：经常玩斗地主，关注游戏数据和统计
- 需求：详细的数据统计、历史记录查询、胜率分析
- 使用场景：日常娱乐、技能提升分析

**组织者**
- 特征：经常组织斗地主活动的用户
- 需求：多人管理、快速结算、公平透明的计分
- 使用场景：定期聚会组织、比赛活动

## 3. 核心功能

### 3.1 玩家管理
- 添加/删除/编辑玩家信息
- 玩家头像设置（系统默认地主农民头像）
- 玩家昵称管理
- 常用玩家收藏功能

### 3.2 游戏设置
- 游戏模式选择（经典斗地主、癞子斗地主等）
- 底分设置（1分、2分、5分等）
- 倍数规则设置（炸弹翻倍、春天翻倍等）
- 封顶分数设置

### 3.3 计分功能
- 实时计分录入
- 地主/农民角色分配
- 胜负结果记录
- 倍数计算（明牌、春天、反春天等）
- 自动结算功能

### 3.4 历史记录
- 游戏历史查询
- 详细对局记录

### 3.5 统计分析
- 个人胜率统计
- 当地主胜率/当农民胜率
- 累计得分排行榜
- 月度/年度统计报告

## 4. 非功能需求

### 4.1 性能要求
- 应用启动时间 < 3秒
- 界面响应时间 < 1秒
- 支持1000+历史记录存储
- 流畅的动画效果

### 4.2 可用性要求
- 界面简洁直观，符合iOS设计规范
- 支持横屏/竖屏切换
- 字体大小适中，适合各年龄段用户
- 操作步骤不超过3步完成主要功能

### 4.3 兼容性要求
- 支持iOS 14.0及以上版本
- 适配iPhone 12/13/14/15系列
- 支持深色/浅色主题切换
- 支持中文简体显示

### 4.4 安全性要求
- 本地数据存储，无网络传输
- 数据备份与恢复功能
- 防误操作确认机制

## 5. 数据模型

### 5.1 玩家实体 (Player)
```
- id: 唯一标识符
- name: 玩家昵称
- avatar: 头像路径
- createTime: 创建时间
- isFavorite: 是否收藏
```

### 5.2 游戏设置实体 (GameSettings)
```
- id: 设置ID
- gameMode: 游戏模式
- baseScore: 底分
- maxMultiplier: 最大倍数
- bombMultiplier: 炸弹倍数
- springMultiplier: 春天倍数
```

### 5.3 游戏记录实体 (GameRecord)
```
- id: 记录ID
- gameDate: 游戏日期
- players: 参与玩家列表
- landlord: 地主玩家ID
- isLandlordWin: 地主是否获胜
- baseScore: 底分
- multiplier: 倍数
- finalScores: 最终得分
- gameSettings: 游戏设置
- createTime: 创建时间
```

### 5.4 统计数据实体 (Statistics)
```
- playerId: 玩家ID
- totalGames: 总游戏局数
- winGames: 获胜局数
- landlordGames: 当地主局数
- landlordWins: 当地主获胜局数
- totalScore: 累计得分
- lastUpdateTime: 最后更新时间
```

## 6. 界面设计要求

### 6.1 主要界面
- 首页：快速开始游戏、查看统计
- 玩家管理页：添加/编辑玩家
- 游戏设置页：配置游戏规则
- 计分页：实时计分录入
- 历史记录页：查看游戏历史
- 统计页：数据分析展示

### 6.2 设计风格
- 采用斗地主经典红绿配色
- 使用扑克牌元素装饰
- 圆角卡片式布局
- 清晰的层次结构

## 7. 技术实现

### 7.1 数据存储
- 使用本地SQLite数据库
- JSON格式配置文件
- 图片文件本地存储

### 7.2 开发框架
- 原生iOS开发或React Native
- 响应式布局设计
- 组件化开发模式

## 8. 项目里程碑

### 阶段一：基础功能开发
- 玩家管理功能
- 基础计分功能
- 简单统计功能

### 阶段二：完善功能
- 高级计分规则
- 详细统计分析
- 历史记录管理

### 阶段三：优化完善
- 界面优化
- 性能优化
- 用户体验提升

## 9. 验收标准

- 所有核心功能正常运行
- 界面美观符合设计规范
- 数据计算准确无误
- 用户操作流畅便捷
- 无明显bug和崩溃问题
