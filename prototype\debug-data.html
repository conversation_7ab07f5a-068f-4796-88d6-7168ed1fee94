<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据调试 - 斗地主计分助手</title>
    <link rel="stylesheet" href="css/common.css">
</head>
<body>
    <div class="phone-container">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="status-time">14:30</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <button class="nav-button back" onclick="Navigation.goBack()">
                    <i class="fas fa-chevron-left"></i> 返回
                </button>
                <div class="nav-title">数据调试</div>
                <div></div>
            </div>

            <!-- 主内容 -->
            <div class="content">
                <div class="card">
                    <div class="card-title">LocalStorage 数据检查</div>
                    <button class="btn btn-primary" onclick="checkData()">检查数据</button>
                    <button class="btn btn-secondary" onclick="clearData()">清除所有数据</button>
                    <button class="btn btn-secondary" onclick="createTestData()">创建测试数据</button>
                </div>

                <div class="card">
                    <div class="card-title">数据详情</div>
                    <div id="dataDetails" style="font-family: monospace; font-size: 12px; white-space: pre-wrap; max-height: 400px; overflow-y: auto; background: #f5f5f5; padding: 10px; border-radius: 8px;">
                        点击"检查数据"按钮查看详情
                    </div>
                </div>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator">
                <div class="home-bar"></div>
            </div>
        </div>
    </div>

    <script src="js/common.js"></script>
    <script>
        function checkData() {
            const dataDetails = document.getElementById('dataDetails');
            let output = '';

            // 检查所有相关的localStorage键
            const keys = [
                'gameRecords',
                'latestGameResult', 
                'gameResult',
                'players',
                'currentGameSession',
                'gameHistories',
                'statistics'
            ];

            keys.forEach(key => {
                const data = localStorage.getItem(key);
                output += `=== ${key} ===\n`;
                if (data) {
                    try {
                        const parsed = JSON.parse(data);
                        output += `类型: ${Array.isArray(parsed) ? 'Array' : typeof parsed}\n`;
                        output += `长度/大小: ${Array.isArray(parsed) ? parsed.length : Object.keys(parsed).length}\n`;
                        output += `内容: ${JSON.stringify(parsed, null, 2)}\n`;
                    } catch (e) {
                        output += `原始数据: ${data}\n`;
                    }
                } else {
                    output += '无数据\n';
                }
                output += '\n';
            });

            // 使用GameRecordManager检查
            output += '=== GameRecordManager.getRecords() ===\n';
            try {
                const records = GameRecordManager.getRecords();
                output += `记录数量: ${records.length}\n`;
                output += `记录内容: ${JSON.stringify(records, null, 2)}\n`;
            } catch (e) {
                output += `错误: ${e.message}\n`;
            }

            dataDetails.textContent = output;
        }

        function clearData() {
            if (confirm('确定要清除所有数据吗？')) {
                const keys = [
                    'gameRecords',
                    'latestGameResult', 
                    'gameResult',
                    'players',
                    'currentGameSession',
                    'gameHistories',
                    'statistics'
                ];
                
                keys.forEach(key => {
                    localStorage.removeItem(key);
                });
                
                alert('所有数据已清除');
                checkData();
            }
        }

        function createTestData() {
            // 创建测试玩家
            const testPlayers = [
                { id: 'player1', name: '张三', avatar: 'landlord' },
                { id: 'player2', name: '李四', avatar: 'farmer' },
                { id: 'player3', name: '王五', avatar: 'farmer' }
            ];
            
            // 创建测试游戏记录
            const testRecord = {
                id: Date.now().toString(),
                createTime: new Date().toISOString(),
                timestamp: new Date().toISOString(),
                gameDate: new Date().toISOString().split('T')[0],
                landlord: 'player1',
                players: ['player1', 'player2', 'player3'],
                baseScore: 3,
                callMultiplier: 1,
                bombCount: 0,
                isSpring: false,
                isAntiSpring: false,
                isShowCard: false,
                multiplier: 1,
                isLandlordWin: true,
                gameMode: 'classic',
                scores: [
                    { playerId: 'player1', score: 6, isLandlord: true },
                    { playerId: 'player2', score: -3, isLandlord: false },
                    { playerId: 'player3', score: -3, isLandlord: false }
                ],
                finalScores: {
                    'player1': 6,
                    'player2': -3,
                    'player3': -3
                }
            };

            // 保存测试数据
            localStorage.setItem('players', JSON.stringify(testPlayers));
            
            // 使用GameRecordManager保存记录
            try {
                GameRecordManager.addRecord(testRecord);
                alert('测试数据已创建');
                checkData();
            } catch (e) {
                alert('创建测试数据失败: ' + e.message);
            }
        }

        // 页面加载时自动检查数据
        document.addEventListener('DOMContentLoaded', function() {
            checkData();
        });
    </script>
</body>
</html>
