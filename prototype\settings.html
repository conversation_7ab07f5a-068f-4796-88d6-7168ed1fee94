<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置 - 斗地主计分助手</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="phone-container">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="status-time">14:30</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <button class="nav-button back" onclick="Navigation.goBack()">
                    <i class="fas fa-chevron-left"></i> 返回
                </button>
                <div class="nav-title">设置</div>
                <div></div>
            </div>

            <!-- 主内容 -->
            <div class="content">
                <!-- 游戏设置 -->
                <div class="card fade-in">
                    <div class="card-title">
                        <i class="fas fa-gamepad" style="color: #007AFF; margin-right: 8px;"></i>
                        游戏设置
                    </div>
                    <div class="settings-list">
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-name">默认游戏模式</div>
                                <div class="setting-desc">设置开始游戏时的默认模式</div>
                            </div>
                            <select id="defaultGameMode" class="setting-select">
                                <option value="classic">经典斗地主</option>
                                <option value="joker">癞子斗地主</option>
                            </select>
                        </div>

                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-name">默认底分</div>
                                <div class="setting-desc">设置开始游戏时的默认底分</div>
                            </div>
                            <select id="defaultBaseScore" class="setting-select">
                                <option value="1">1分</option>
                                <option value="2">2分</option>
                                <option value="5">5分</option>
                                <option value="10">10分</option>
                            </select>
                        </div>

                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-name">炸弹翻倍</div>
                                <div class="setting-desc">是否启用炸弹翻倍规则</div>
                            </div>
                            <label class="switch">
                                <input type="checkbox" id="enableBombMultiplier" checked>
                                <span class="slider"></span>
                            </label>
                        </div>

                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-name">春天翻倍</div>
                                <div class="setting-desc">是否启用春天翻倍规则</div>
                            </div>
                            <label class="switch">
                                <input type="checkbox" id="enableSpringMultiplier" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- 界面设置 -->
                <div class="card fade-in">
                    <div class="card-title">
                        <i class="fas fa-palette" style="color: #6F42C1; margin-right: 8px;"></i>
                        界面设置
                    </div>
                    <div class="settings-list">
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-name">主题模式</div>
                                <div class="setting-desc">选择应用的主题外观</div>
                            </div>
                            <select id="themeMode" class="setting-select" onchange="changeTheme()">
                                <option value="light">浅色主题</option>
                                <option value="dark">深色主题</option>
                                <option value="auto">跟随系统</option>
                            </select>
                        </div>

                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-name">动画效果</div>
                                <div class="setting-desc">是否启用页面切换动画</div>
                            </div>
                            <label class="switch">
                                <input type="checkbox" id="enableAnimations" checked>
                                <span class="slider"></span>
                            </label>
                        </div>

                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-name">声音提示</div>
                                <div class="setting-desc">是否启用操作声音提示</div>
                            </div>
                            <label class="switch">
                                <input type="checkbox" id="enableSounds" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- 数据管理 -->
                <div class="card fade-in">
                    <div class="card-title">
                        <i class="fas fa-database" style="color: #28A745; margin-right: 8px;"></i>
                        数据管理
                    </div>
                    <div class="settings-list">
                        <div class="setting-item clickable" onclick="clearAllData()">
                            <div class="setting-info">
                                <div class="setting-name">清除所有数据</div>
                                <div class="setting-desc">删除所有游戏记录和玩家数据</div>
                            </div>
                            <i class="fas fa-trash setting-arrow" style="color: #DC3545;"></i>
                        </div>
                    </div>
                </div>

                <!-- 关于应用 -->
                <div class="card fade-in">
                    <div class="card-title">
                        <i class="fas fa-info-circle" style="color: #FFA500; margin-right: 8px;"></i>
                        关于应用
                    </div>
                    <div class="about-content">
                        <div class="app-icon">
                            <div style="font-size: 48px; color: #DC143C;">
                                <span>♠</span><span style="color: #FF0000;">♥</span><span style="color: #000;">♣</span><span style="color: #FF0000;">♦</span>
                            </div>
                        </div>
                        <div class="app-info">
                            <h3>斗地主计分助手</h3>
                            <p>版本 1.2.0</p>
                            <p style="font-size: 14px; color: #666; margin-top: 8px;">
                                一款专为斗地主爱好者设计的本地计分应用，支持多种游戏模式的积分记录、统计分析和历史查询功能。
                            </p>
                            <div class="app-stats">
                                <div class="stat-item">
                                    <span class="stat-label">数据存储:</span>
                                    <span class="stat-value" id="storageUsed">计算中...</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">总游戏局数:</span>
                                    <span class="stat-value" id="totalGamesCount">0</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">注册玩家:</span>
                                    <span class="stat-value" id="totalPlayersCount">0</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">最后更新:</span>
                                    <span class="stat-value" id="lastUpdateTime">-</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-list" style="margin-top: 16px;">
                        <div class="setting-item clickable" onclick="showUpdateLog()">
                            <div class="setting-info">
                                <div class="setting-name">更新日志</div>
                                <div class="setting-desc">查看版本更新历史</div>
                            </div>
                            <i class="fas fa-chevron-right setting-arrow"></i>
                        </div>

                        <div class="setting-item clickable" onclick="showPrivacyPolicy()">
                            <div class="setting-info">
                                <div class="setting-name">隐私政策</div>
                                <div class="setting-desc">了解数据使用和隐私保护</div>
                            </div>
                            <i class="fas fa-chevron-right setting-arrow"></i>
                        </div>

                        <div class="setting-item clickable" onclick="showHelp()">
                            <div class="setting-info">
                                <div class="setting-name">使用帮助</div>
                                <div class="setting-desc">查看应用使用指南</div>
                            </div>
                            <i class="fas fa-chevron-right setting-arrow"></i>
                        </div>
                    </div>
                </div>

                <!-- 保存按钮 -->
                <button class="btn btn-primary btn-large btn-full fade-in" onclick="saveSettings()">
                    <i class="fas fa-save" style="margin-right: 8px;"></i>
                    保存设置
                </button>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator">
                <div class="home-bar"></div>
            </div>
        </div>
    </div>

    <style>
        .app-stats {
            margin-top: 16px;
            padding: 16px;
            background: #F8F9FA;
            border-radius: 8px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #E0E0E0;
        }

        .stat-item:last-child {
            border-bottom: none;
        }

        .stat-label {
            font-size: 14px;
            color: #666;
            font-weight: 500;
        }

        .stat-value {
            font-size: 14px;
            color: #333;
            font-weight: 600;
        }

        /* 深色主题样式 */
        .dark-theme {
            background: #1a1a1a;
            color: #ffffff;
        }

        .dark-theme .screen {
            background: #1a1a1a;
        }

        .dark-theme .card {
            background: #2d2d2d;
            border-color: #404040;
        }

        .dark-theme .card-title {
            color: #ffffff;
        }

        .dark-theme .setting-name {
            color: #ffffff;
        }

        .dark-theme .setting-desc {
            color: #cccccc;
        }

        .dark-theme .setting-select {
            background: #404040;
            color: #ffffff;
            border-color: #555555;
        }

        .dark-theme .app-stats {
            background: #404040;
        }

        .dark-theme .stat-label {
            color: #cccccc;
        }

        .dark-theme .stat-value {
            color: #ffffff;
        }

        .dark-theme .stat-item {
            border-bottom-color: #555555;
        }

        /* 隐藏文件输入 */
        #fileInput {
            display: none;
        }
    </style>

    <script src="js/common.js"></script>
    <script>
        // 页面加载完成后加载设置
        document.addEventListener('DOMContentLoaded', function() {
            loadSettings();

            // 监听数据更新事件，实现实时同步
            window.addEventListener('gameDataUpdated', function() {
                updateDataStatistics();
            });

            window.addEventListener('playerDataUpdated', function() {
                updateDataStatistics();
            });
        });

        function loadSettings() {
            const settings = GameSettingsManager.getSettings();

            // 加载游戏设置
            document.getElementById('defaultGameMode').value = settings.gameMode || 'classic';
            document.getElementById('defaultBaseScore').value = settings.baseScore || 1;
            document.getElementById('enableBombMultiplier').checked = settings.bombMultiplier !== false;
            document.getElementById('enableSpringMultiplier').checked = settings.springMultiplier !== false;

            // 加载界面设置
            const appSettings = LocalStorage.getItem('appSettings') || {};
            document.getElementById('themeMode').value = appSettings.themeMode || 'light';
            document.getElementById('enableAnimations').checked = appSettings.enableAnimations !== false;
            document.getElementById('enableSounds').checked = appSettings.enableSounds !== false;


            // 应用主题
            applyTheme(appSettings.themeMode || 'light');

            // 显示数据统计
            updateDataStatistics();
        }

        function saveSettings() {
            // 保存游戏设置
            const gameSettings = {
                gameMode: document.getElementById('defaultGameMode').value,
                baseScore: parseInt(document.getElementById('defaultBaseScore').value),
                bombMultiplier: document.getElementById('enableBombMultiplier').checked,
                springMultiplier: document.getElementById('enableSpringMultiplier').checked
            };

            // 保存应用设置
            const appSettings = {
                themeMode: document.getElementById('themeMode').value,
                enableAnimations: document.getElementById('enableAnimations').checked,
                enableSounds: document.getElementById('enableSounds').checked
            };

            try {
                GameSettingsManager.updateSettings(gameSettings);
                LocalStorage.setItem('appSettings', appSettings);
                Utils.showToast('设置已保存', 'success');
            } catch (error) {
                Utils.showToast('保存失败，请重试', 'error');
                console.error('Save settings error:', error);
            }
        }

        function updateDataStatistics() {
            const players = PlayerManager.getPlayers();
            const records = GameRecordManager.getRecords();

            document.getElementById('totalPlayersCount').textContent = players.length;
            document.getElementById('totalGamesCount').textContent = records.length;

            // 计算数据大小（估算）
            const dataSize = JSON.stringify({
                players: players,
                records: records
            }).length;

            const sizeInKB = (dataSize / 1024).toFixed(1);
            document.getElementById('storageUsed').textContent = sizeInKB + ' KB';

            // 更新最后更新时间
            const lastRecord = records.length > 0 ? records[records.length - 1] : null;
            const lastUpdateTime = lastRecord ?
                new Date(lastRecord.timestamp).toLocaleDateString('zh-CN') :
                '无记录';
            document.getElementById('lastUpdateTime').textContent = lastUpdateTime;
        }



        function clearGameData() {
            const records = GameRecordManager.getRecords();
            if (records.length === 0) {
                Utils.showToast('没有游戏记录可以清除', 'warning');
                return;
            }

            Utils.confirm(`确定要清除所有游戏记录吗？\n\n将删除 ${records.length} 条游戏记录，此操作不可恢复。`, () => {
                try {
                    LocalStorage.removeItem('gameRecords');
                    LocalStorage.removeItem('latestGameResult');
                    LocalStorage.removeItem('currentGameSession');

                    Utils.showToast('游戏记录已清除', 'success');
                    updateDataStatistics();
                } catch (error) {
                    Utils.showToast('清除失败，请重试', 'error');
                    console.error('Clear game data error:', error);
                }
            });
        }

        function clearPlayerData() {
            const players = PlayerManager.getPlayers();
            const records = GameRecordManager.getRecords();

            if (players.length === 0) {
                Utils.showToast('没有玩家数据可以清除', 'warning');
                return;
            }

            let message = `确定要清除所有玩家数据吗？\n\n将删除 ${players.length} 个玩家`;
            if (records.length > 0) {
                message += `\n\n注意：还有 ${records.length} 条游戏记录，建议先清除游戏记录。`;
            }
            message += '\n\n此操作不可恢复。';

            Utils.confirm(message, () => {
                try {
                    LocalStorage.removeItem('players');
                    Utils.showToast('玩家数据已清除', 'success');
                    updateDataStatistics();
                } catch (error) {
                    Utils.showToast('清除失败，请重试', 'error');
                    console.error('Clear player data error:', error);
                }
            });
        }

        function clearAllData() {
            const players = PlayerManager.getPlayers();
            const records = GameRecordManager.getRecords();

            if (players.length === 0 && records.length === 0) {
                Utils.showToast('没有数据可以清除', 'warning');
                return;
            }

            Utils.confirm('确定要清除所有数据吗？此操作不可恢复，包括：\n• 所有玩家信息\n• 所有游戏记录\n• 所有应用设置', () => {
                try {
                    // 清除所有数据
                    LocalStorage.removeItem('players');
                    LocalStorage.removeItem('gameRecords');
                    LocalStorage.removeItem('appSettings');
                    LocalStorage.removeItem('currentGameSettings');
                    LocalStorage.removeItem('currentGameSession');
                    LocalStorage.removeItem('latestGameResult');

                    Utils.showToast('所有数据已清除', 'success');
                    updateDataStatistics();

                    // 重新加载设置
                    setTimeout(() => {
                        loadSettings();
                    }, 500);
                } catch (error) {
                    Utils.showToast('清除失败，请重试', 'error');
                    console.error('Clear all data error:', error);
                }
            });
        }

        function resetSettings() {
            Utils.confirm('确定要重置所有设置为默认值吗？', () => {
                try {
                    LocalStorage.removeItem('gameSettings');
                    LocalStorage.removeItem('appSettings');

                    Utils.showToast('设置已重置', 'success');
                    loadSettings();
                } catch (error) {
                    Utils.showToast('重置失败，请重试', 'error');
                    console.error('Reset settings error:', error);
                }
            });
        }

        function changeTheme() {
            const themeMode = document.getElementById('themeMode').value;
            applyTheme(themeMode);
        }

        function applyTheme(theme) {
            const body = document.body;

            if (theme === 'dark') {
                body.classList.add('dark-theme');
            } else if (theme === 'light') {
                body.classList.remove('dark-theme');
            } else if (theme === 'auto') {
                // 检测系统主题
                const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
                if (prefersDark) {
                    body.classList.add('dark-theme');
                } else {
                    body.classList.remove('dark-theme');
                }
            }
        }





        function showUpdateLog() {
            Utils.showModal('更新日志', `
                <div style="text-align: left; line-height: 1.6;">
                    <h4>版本 1.2.0 (当前版本)</h4>
                    <ul>
                        <li>新增明牌功能和封顶分数设置</li>
                        <li>添加计分历史和撤销功能</li>
                        <li>增强统计分析，支持时间筛选</li>
                        <li>新增主题切换功能</li>

                        <li>优化界面布局和用户体验</li>
                    </ul>

                    <h4>版本 1.1.0</h4>
                    <ul>
                        <li>添加玩家收藏功能</li>
                        <li>增强搜索和筛选功能</li>
                        <li>优化数据同步机制</li>
                        <li>修复已知问题</li>
                    </ul>

                    <h4>版本 1.0.0</h4>
                    <ul>
                        <li>基础计分功能</li>
                        <li>玩家管理</li>
                        <li>历史记录查看</li>
                        <li>统计分析</li>
                    </ul>
                </div>
            `);
        }

        function showPrivacyPolicy() {
            Utils.showModal('隐私政策', `
                <div style="text-align: left; line-height: 1.6;">
                    <h4>数据收集</h4>
                    <p>本应用仅在本地存储您的游戏数据，不会收集或上传任何个人信息到服务器。</p>

                    <h4>数据使用</h4>
                    <p>所有数据仅用于应用功能实现，包括：</p>
                    <ul>
                        <li>游戏记录存储和查看</li>
                        <li>玩家信息管理</li>
                        <li>统计分析计算</li>
                        <li>应用设置保存</li>
                    </ul>

                    <h4>数据安全</h4>
                    <p>所有数据存储在您的设备本地，您可以随时通过设置页面清除数据。</p>

                    <h4>联系我们</h4>
                    <p>如有任何隐私相关问题，请通过应用内反馈功能联系我们。</p>
                </div>
            `);
        }

        function showHelp() {
            Utils.showModal('使用帮助', `
                <div style="text-align: left; line-height: 1.6;">
                    <h4>快速开始</h4>
                    <ol>
                        <li>在"玩家管理"中添加玩家</li>
                        <li>在"游戏设置"中选择玩家和规则</li>
                        <li>在"计分页面"进行游戏计分</li>
                        <li>查看"历史记录"和"统计分析"</li>
                    </ol>

                    <h4>功能说明</h4>
                    <ul>
                        <li><strong>明牌功能：</strong>勾选后倍数×2</li>
                        <li><strong>封顶分数：</strong>限制最大倍数</li>
                        <li><strong>计分历史：</strong>支持撤销上一局</li>
                        <li><strong>玩家收藏：</strong>点击星标收藏常用玩家</li>

                    </ul>

                    <h4>常见问题</h4>
                    <p><strong>Q: 数据会丢失吗？</strong><br>
                    A: 数据存储在本地，请注意保护设备数据安全。</p>
                </div>
            `);
        }
    </script>

    <style>
        .settings-list {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .setting-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px 16px;
            border-bottom: 1px solid #F0F0F0;
            border-radius: 8px;
            transition: all 0.2s;
        }

        .setting-item:last-child {
            border-bottom: none;
        }

        .setting-item.clickable {
            cursor: pointer;
        }

        .setting-item.clickable:hover {
            background: rgba(0,0,0,0.02);
            transform: translateX(4px);
        }

        .setting-info {
            flex: 1;
        }

        .setting-name {
            font-size: 17px;
            font-weight: 600;
            margin-bottom: 6px;
            color: #333;
        }

        .setting-desc {
            font-size: 13px;
            color: #666;
            line-height: 1.4;
        }

        .setting-select {
            padding: 8px 12px;
            border: 1px solid #E0E0E0;
            border-radius: 6px;
            font-size: 14px;
            background: white;
            min-width: 120px;
        }

        .setting-arrow {
            color: #666;
            font-size: 16px;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #007AFF;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .about-content {
            text-align: center;
            padding: 32px 20px;
        }

        .app-icon {
            margin-bottom: 20px;
        }

        .app-icon div {
            font-size: 56px;
            letter-spacing: 4px;
        }

        .app-info h3 {
            font-size: 24px;
            font-weight: 700;
            color: #333;
            margin-bottom: 8px;
        }

        .app-info p {
            color: #666;
            margin-bottom: 8px;
            line-height: 1.5;
        }

        .app-info .version {
            font-size: 14px;
            color: #999;
        }

        .app-info .description {
            font-size: 14px;
            margin-top: 16px;
            padding: 16px;
            background: rgba(0,0,0,0.02);
            border-radius: 8px;
        }
    </style>
</body>
</html>
