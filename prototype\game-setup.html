<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游戏设置 - 斗地主计分助手</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="phone-container">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="status-time">14:30</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <button class="nav-button back" onclick="Navigation.goBack()">
                    <i class="fas fa-chevron-left"></i> 返回
                </button>
                <div class="nav-title">游戏设置</div>
                <div></div>
            </div>

            <!-- 主内容 -->
            <div class="content">
                <!-- 选择玩家 -->
                <div class="card fade-in">
                    <div class="card-title">
                        <i class="fas fa-users" style="color: #007AFF; margin-right: 8px;"></i>
                        选择玩家 (需要3人)
                        <button class="btn-icon" onclick="toggleFavoriteFilter()" id="favoriteFilterBtn" title="显示收藏玩家">
                            <i class="fas fa-star"></i>
                        </button>
                    </div>
                    <div class="player-filter-tabs">
                        <button class="filter-tab active" data-filter="all" onclick="filterPlayers('all')">
                            <i class="fas fa-users"></i> 全部玩家
                        </button>
                        <button class="filter-tab" data-filter="favorite" onclick="filterPlayers('favorite')">
                            <i class="fas fa-star"></i> 收藏玩家
                        </button>
                        <button class="filter-tab" data-filter="recent" onclick="filterPlayers('recent')">
                            <i class="fas fa-clock"></i> 最近游戏
                        </button>
                    </div>

                    <!-- 添加玩家按钮区域 -->
                    <div class="add-player-section">
                        <button class="btn btn-outline add-player-btn" onclick="showQuickAddPlayer()">
                            <i class="fas fa-user-plus"></i>
                            添加玩家
                        </button>
                    </div>

                    <div id="playerSelection">
                        <!-- 玩家选择将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 游戏规则设置 -->
                <div class="card fade-in">
                    <div class="card-title">
                        <i class="fas fa-cog" style="color: #28A745; margin-right: 8px;"></i>
                        游戏规则
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">游戏模式</label>
                        <select id="gameMode" class="setting-select" onchange="updateGameModeDescription()">
                            <option value="classic">经典斗地主</option>
                            <option value="joker">癞子斗地主</option>
                        </select>
                        <div class="game-mode-description" id="gameModeDescription">
                            <i class="fas fa-info-circle"></i>
                            <span id="modeDescText">经典斗地主：使用标准54张牌，包含大小王</span>
                        </div>
                    </div>

                    <div class="setting-item">
                        <label class="setting-label">底分</label>
                        <div class="score-selector">
                            <button class="score-btn" data-score="1">1分</button>
                            <button class="score-btn" data-score="2">2分</button>
                            <button class="score-btn" data-score="5">5分</button>
                            <button class="score-btn" data-score="10">10分</button>
                        </div>
                    </div>

                    <div class="setting-item">
                        <label class="setting-label">倍数规则</label>
                        <div class="multiplier-settings">
                            <div class="multiplier-item">
                                <span>炸弹翻倍</span>
                                <label class="switch">
                                    <input type="checkbox" id="bombMultiplier" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                            <div class="multiplier-item">
                                <span>春天翻倍</span>
                                <label class="switch">
                                    <input type="checkbox" id="springMultiplier" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                            <div class="multiplier-item">
                                <span>明牌加倍</span>
                                <label class="switch">
                                    <input type="checkbox" id="showCardMultiplier">
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="setting-item">
                        <label class="setting-label">封顶分数</label>
                        <div class="cap-score-selector">
                            <button class="cap-score-btn" data-cap="0">不封顶</button>
                            <button class="cap-score-btn" data-cap="32">32倍</button>
                            <button class="cap-score-btn" data-cap="64">64倍</button>
                            <button class="cap-score-btn" data-cap="128">128倍</button>
                        </div>
                        <div class="setting-description">
                            <i class="fas fa-info-circle"></i>
                            设置单局最大倍数，防止分数过高
                        </div>
                    </div>
                </div>

                <!-- 开始游戏按钮 -->
                <button class="btn btn-primary btn-large btn-full fade-in" onclick="startGame()" id="startGameBtn" disabled>
                    <i class="fas fa-play" style="margin-right: 8px;"></i>
                    开始游戏
                </button>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator">
                <div class="home-bar"></div>
            </div>
        </div>
    </div>

    <style>
        .player-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 12px;
        }

        .player-option {
            display: flex;
            align-items: center;
            padding: 16px;
            border: 2px solid #E0E0E0;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.2s;
            background: white;
        }

        .player-option:hover {
            border-color: #007AFF;
            background: rgba(0,122,255,0.05);
        }

        .player-option.selected {
            border-color: #DC143C;
            background: linear-gradient(135deg, #FFF8DC, #FFFACD);
        }

        .player-option.disabled {
            opacity: 0.4;
            cursor: not-allowed;
            background: #F8F9FA !important;
            border-color: #E0E0E0 !important;
        }

        .player-option.disabled:hover {
            border-color: #E0E0E0 !important;
            background: #F8F9FA !important;
            transform: none !important;
            box-shadow: none !important;
        }

        .player-option.disabled .avatar {
            filter: grayscale(50%);
        }

        .player-option.disabled .player-name {
            color: #999 !important;
        }

        .player-option.disabled .selection-indicator i {
            color: #CCC !important;
        }

        .player-info {
            flex: 1;
            margin-left: 4px;
        }

        .player-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .setting-item {
            margin-bottom: 24px;
        }

        .setting-item:last-child {
            margin-bottom: 0;
        }

        .setting-label {
            display: block;
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }

        .setting-select {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #E0E0E0;
            border-radius: 8px;
            font-size: 16px;
            background: white;
            transition: border-color 0.2s;
        }

        .setting-select:focus {
            outline: none;
            border-color: #007AFF;
            box-shadow: 0 0 0 3px rgba(0,122,255,0.1);
        }

        .score-selector {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 12px;
        }

        .score-btn {
            padding: 12px 8px;
            border: 2px solid #E0E0E0;
            border-radius: 8px;
            background: white;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            color: #333;
        }

        .score-btn:hover {
            border-color: #007AFF;
            background: rgba(0,122,255,0.05);
        }

        .score-btn.selected {
            border-color: #DC143C;
            background: linear-gradient(135deg, #DC143C, #B22222);
            color: white;
        }

        .multiplier-settings {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .multiplier-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            background: linear-gradient(135deg, #F8F9FA, #E9ECEF);
            border-radius: 10px;
        }

        .multiplier-item span {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #DC143C;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .no-players {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        .no-players i {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
            color: #DC143C;
        }

        .no-players p {
            margin-bottom: 8px;
            font-size: 16px;
        }

        .no-players .subtitle {
            font-size: 14px;
            opacity: 0.7;
        }

        .selected-count {
            display: inline-block;
            background: linear-gradient(135deg, #DC143C, #B22222);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            margin-left: 8px;
        }

        .add-player-section {
            margin: 16px 0;
            text-align: center;
        }

        .add-player-btn {
            padding: 12px 24px;
            border: 2px solid #007AFF;
            background: white;
            color: #007AFF;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .add-player-btn:hover {
            background: #007AFF;
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,122,255,0.3);
        }

        .add-player-options {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .add-player-modal {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin: 20px;
            max-width: 400px;
            width: 100%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            color: #666;
            cursor: pointer;
            padding: 0;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background-color 0.2s;
        }

        .close-btn:hover {
            background: #f0f0f0;
        }

        .add-option {
            display: flex;
            align-items: center;
            padding: 16px;
            border: 2px solid #E0E0E0;
            border-radius: 12px;
            margin-bottom: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .add-option:hover {
            border-color: #007AFF;
            background: rgba(0,122,255,0.05);
        }

        .add-option i {
            font-size: 24px;
            margin-right: 16px;
            color: #007AFF;
        }

        .add-option-content h3 {
            margin: 0 0 4px 0;
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .add-option-content p {
            margin: 0;
            font-size: 14px;
            color: #666;
        }

        .avatar-option {
            transition: all 0.2s;
        }

        .avatar-option:hover {
            transform: scale(1.1);
        }

        .avatar-option.selected {
            border-color: #007AFF !important;
            box-shadow: 0 0 0 3px rgba(0,122,255,0.2);
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .player-option {
            transition: all 0.2s ease;
        }

        .player-option:hover:not(.disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .player-option.selected {
            animation: pulse 0.3s ease;
        }
    </style>

    <script src="js/common.js"></script>
    <script>
        let selectedPlayers = [];
        let currentPlayerFilter = 'all';
        let gameSettings = {
            gameMode: 'classic',
            baseScore: 1,
            bombMultiplier: true,
            springMultiplier: true,
            showCardMultiplier: false,
            capScore: 0
        };

        // 检查玩家数据完整性
        function checkPlayerDataIntegrity() {
            const players = PlayerManager.getPlayers();
            console.log('=== Player Data Integrity Check ===');
            console.log('Total players:', players.length);

            const playerIds = players.map(p => p.id);
            const uniqueIds = [...new Set(playerIds)];

            if (playerIds.length !== uniqueIds.length) {
                console.error('DUPLICATE PLAYER IDs FOUND!');
                console.log('All IDs:', playerIds);
                console.log('Unique IDs:', uniqueIds);
            } else {
                console.log('No duplicate IDs found');
            }

            players.forEach((player, index) => {
                console.log(`Player ${index}: ${player.name} (ID: ${player.id})`);
            });
            console.log('=== End Integrity Check ===');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 清除之前的选择状态
            selectedPlayers = [];

            // 检查数据完整性
            checkPlayerDataIntegrity();

            loadPlayers();
            initializeSettings();

            // 监听数据更新事件，实现实时同步
            window.addEventListener('playerDataUpdated', function() {
                loadPlayers();
            });

            window.addEventListener('settingsUpdated', function() {
                initializeSettings();
            });
        });

        function filterPlayers(filterType) {
            currentPlayerFilter = filterType;

            // 更新筛选按钮状态
            document.querySelectorAll('.filter-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[data-filter="${filterType}"]`).classList.add('active');

            loadPlayers();
        }

        function filterPlayersByType(players, filterType) {
            switch(filterType) {
                case 'favorite':
                    return players.filter(player => player.isFavorite);
                case 'recent':
                    // 获取最近游戏的玩家
                    const recentRecords = GameRecordManager.getRecords().slice(0, 10); // 最近10局
                    const recentPlayerIds = new Set();
                    recentRecords.forEach(record => {
                        if (record.players && Array.isArray(record.players)) {
                            record.players.forEach(playerId => recentPlayerIds.add(playerId));
                        }
                    });
                    return players.filter(player => recentPlayerIds.has(player.id));
                default:
                    return players;
            }
        }

        function updateGameModeDescription() {
            const gameMode = document.getElementById('gameMode').value;
            const descText = document.getElementById('modeDescText');

            if (gameMode === 'classic') {
                descText.textContent = '经典斗地主：使用标准54张牌，包含大小王';
            } else if (gameMode === 'joker') {
                descText.textContent = '癞子斗地主：指定一张牌作为万能牌，增加游戏趣味性';
            }

            gameSettings.gameMode = gameMode;
        }

        function loadPlayers() {
            console.log('=== loadPlayers START ===');
            console.log('selectedPlayers in loadPlayers:', JSON.stringify(selectedPlayers));

            const allPlayers = PlayerManager.getPlayers();
            const playerSelectionContainer = document.getElementById('playerSelection');

            console.log('allPlayers count:', allPlayers.length);

            if (allPlayers.length < 3) {
                playerSelectionContainer.innerHTML = `
                    <div class="no-players">
                        <i class="fas fa-exclamation-triangle"></i>
                        <p>至少需要3个玩家才能开始游戏</p>
                        <p class="subtitle">当前只有${allPlayers.length}个玩家</p>
                        <button class="btn btn-primary" onclick="Navigation.goTo('players.html')" style="margin-top: 16px;">
                            <i class="fas fa-user-plus" style="margin-right: 8px;"></i>
                            添加玩家
                        </button>
                    </div>
                `;
                return;
            }

            // 应用筛选
            const filteredPlayers = filterPlayersByType(allPlayers, currentPlayerFilter);

            // 更新标题显示选中数量
            updatePlayerSelectionTitle();

            if (filteredPlayers.length === 0) {
                playerSelectionContainer.innerHTML = `
                    <div class="no-players">
                        <i class="fas fa-filter"></i>
                        <p>当前筛选条件下没有玩家</p>
                        <p class="subtitle">请尝试其他筛选条件</p>
                    </div>
                `;
                return;
            }

            playerSelectionContainer.innerHTML = `
                <div class="player-grid">
                    ${filteredPlayers.map(player => {
                        const avatarIcon = getAvatarIcon(player.avatar);
                        const isSelected = selectedPlayers.includes(player.id);
                        const isDisabled = !isSelected && selectedPlayers.length >= 3;

                        console.log(`Player ${player.name} (${player.id}): selected=${isSelected}, disabled=${isDisabled}`);

                        return `
                            <div class="player-option ${isSelected ? 'selected' : ''} ${isDisabled ? 'disabled' : ''}"
                                 data-player-id="${player.id}"
                                 onclick="togglePlayer('${player.id}')">
                                <div class="avatar" style="background: ${getPlayerAvatarColor(player)};">
                                    ${avatarIcon}
                                </div>
                                <div class="player-info">
                                    <div class="player-name">${player.name}</div>
                                </div>
                                <div class="selection-indicator">
                                    <i class="fas ${isSelected ? 'fa-check-circle' : 'fa-circle'}"></i>
                                </div>
                            </div>
                        `;
                    }).join('')}
                </div>
            `;

            console.log('=== loadPlayers END ===');
        }

        function updatePlayerSelectionTitle() {
            const titleElement = document.querySelector('.card-title');
            if (!titleElement) return;

            // 移除现有的计数器
            const existingCount = titleElement.querySelector('.selected-count');
            if (existingCount) {
                existingCount.remove();
            }

            // 创建新的计数器
            const count = selectedPlayers.length;
            const countSpan = document.createElement('span');
            countSpan.className = 'selected-count';
            countSpan.textContent = `${count}/3`;

            // 根据选择状态改变样式
            if (count === 3) {
                countSpan.style.background = 'linear-gradient(135deg, #28A745, #20C997)';
                countSpan.style.animation = 'pulse 0.5s ease';
            } else if (count > 0) {
                countSpan.style.background = 'linear-gradient(135deg, #FFC107, #FF8C00)';
                countSpan.style.animation = '';
            } else {
                countSpan.style.background = 'linear-gradient(135deg, #DC143C, #B22222)';
                countSpan.style.animation = '';
            }

            titleElement.appendChild(countSpan);
        }

        function getPlayerAvatarColor(player) {
            const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#F8B500', '#FF8C94'];
            const index = player.name.charCodeAt(0) % colors.length;
            return colors[index];
        }

        function getAvatarIcon(avatarType) {
            switch(avatarType) {
                case 'crown':
                    return '<i class="fas fa-crown" style="color: white; font-size: 20px;"></i>';
                case 'star':
                    return '<i class="fas fa-star" style="color: white; font-size: 20px;"></i>';
                case 'heart':
                    return '<i class="fas fa-heart" style="color: white; font-size: 20px;"></i>';
                case 'default':
                default:
                    return '<i class="fas fa-user" style="color: white; font-size: 20px;"></i>';
            }
        }

        function togglePlayer(playerId) {
            console.log('=== togglePlayer START ===');
            console.log('playerId:', playerId);
            console.log('selectedPlayers before:', JSON.stringify(selectedPlayers));

            // 防止重复调用
            if (window.togglePlayerLock) {
                console.log('Function locked, returning');
                return;
            }
            window.togglePlayerLock = true;

            setTimeout(() => {
                window.togglePlayerLock = false;
            }, 100);

            const player = PlayerManager.getPlayer(playerId);
            if (!player) {
                console.log('Player not found');
                Utils.showToast('玩家不存在', 'error');
                return;
            }
            console.log('Player found:', player.name);

            const isSelected = selectedPlayers.includes(playerId);
            console.log('isSelected:', isSelected);

            if (isSelected) {
                // 取消选择
                console.log('Deselecting player');
                selectedPlayers = selectedPlayers.filter(id => id !== playerId);
                Utils.showToast(`已取消选择"${player.name}"`, 'info');
            } else {
                // 选择玩家
                if (selectedPlayers.length >= 3) {
                    console.log('Already 3 players selected');
                    Utils.showToast('最多只能选择3个玩家', 'warning');
                    return;
                }

                console.log('Selecting player');
                selectedPlayers.push(playerId);
                Utils.showToast(`已选择"${player.name}"`, 'success');
            }

            console.log('selectedPlayers after:', JSON.stringify(selectedPlayers));
            console.log('=== togglePlayer END ===');

            // 重新渲染玩家列表以确保状态同步
            loadPlayers();
            updateStartButton();
        }



        function updateStartButton() {
            const startBtn = document.getElementById('startGameBtn');
            if (!startBtn) return;

            if (selectedPlayers.length === 3) {
                startBtn.disabled = false;
                startBtn.style.opacity = '1';
                startBtn.style.transform = 'scale(1)';
                startBtn.style.background = 'linear-gradient(135deg, #28A745, #20C997)';
                startBtn.innerHTML = '<i class="fas fa-play" style="margin-right: 8px;"></i>开始游戏';

                // 添加准备就绪的动画
                startBtn.style.animation = 'pulse 1s infinite';
            } else {
                startBtn.disabled = true;
                startBtn.style.opacity = '0.5';
                startBtn.style.transform = 'scale(0.95)';
                startBtn.style.background = 'linear-gradient(135deg, #6C757D, #495057)';
                startBtn.style.animation = '';

                const remaining = 3 - selectedPlayers.length;
                startBtn.innerHTML = `<i class="fas fa-users" style="margin-right: 8px;"></i>还需选择${remaining}个玩家`;
            }
        }

        function initializeSettings() {
            // 初始化底分选择
            document.querySelectorAll('.score-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.score-btn').forEach(b => b.classList.remove('selected'));
                    this.classList.add('selected');
                    gameSettings.baseScore = parseInt(this.dataset.score);
                });
            });

            // 初始化封顶分数选择
            document.querySelectorAll('.cap-score-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.cap-score-btn').forEach(b => b.classList.remove('selected'));
                    this.classList.add('selected');
                    gameSettings.capScore = parseInt(this.dataset.cap);
                });
            });

            // 初始化倍数设置
            document.getElementById('bombMultiplier').addEventListener('change', function() {
                gameSettings.bombMultiplier = this.checked;
            });

            document.getElementById('springMultiplier').addEventListener('change', function() {
                gameSettings.springMultiplier = this.checked;
            });

            document.getElementById('showCardMultiplier').addEventListener('change', function() {
                gameSettings.showCardMultiplier = this.checked;
            });

            // 默认选择1分和不封顶
            document.querySelector('[data-score="1"]').classList.add('selected');
            document.querySelector('[data-cap="0"]').classList.add('selected');
        }

        function startGame() {
            if (selectedPlayers.length !== 3) {
                Utils.showToast('请选择3个玩家', 'error');
                return;
            }

            // 验证玩家是否仍然存在
            const players = PlayerManager.getPlayers();
            const validPlayers = selectedPlayers.filter(playerId =>
                players.some(player => player.id === playerId)
            );

            if (validPlayers.length !== 3) {
                Utils.showToast('部分选中的玩家已被删除，请重新选择', 'error');
                selectedPlayers = validPlayers;
                loadPlayers();
                updateStartButton();
                return;
            }

            // 收集游戏设置
            gameSettings.gameMode = document.getElementById('gameMode').value;
            gameSettings.bombMultiplier = document.getElementById('bombMultiplier').checked;
            gameSettings.springMultiplier = document.getElementById('springMultiplier').checked;
            gameSettings.showCardMultiplier = document.getElementById('showCardMultiplier').checked;

            // 创建游戏会话
            const gameSession = {
                id: Utils.generateId(),
                players: selectedPlayers,
                settings: { ...gameSettings },
                startTime: new Date().toISOString(),
                status: 'playing'
            };

            // 保存游戏设置到localStorage
            LocalStorage.setItem('currentGameSession', gameSession);
            LocalStorage.setItem('currentGameSettings', gameSettings);
            LocalStorage.setItem('currentGamePlayers', selectedPlayers);

            Utils.showToast('游戏开始！', 'success');

            // 跳转到计分页面
            setTimeout(() => {
                Navigation.goTo('scoring.html');
            }, 500);
        }

        // 添加玩家相关函数
        function showAddPlayerOptions() {
            document.getElementById('addPlayerOptions').style.display = 'flex';
        }

        function hideAddPlayerOptions() {
            document.getElementById('addPlayerOptions').style.display = 'none';
        }

        function showQuickAddPlayer() {
            hideAddPlayerOptions();
            document.getElementById('quickAddPlayerModal').style.display = 'flex';
            document.getElementById('quickPlayerName').focus();
        }

        function hideQuickAddPlayer() {
            document.getElementById('quickAddPlayerModal').style.display = 'none';
            document.getElementById('quickPlayerName').value = '';
            // 重置头像选择
            document.querySelectorAll('.avatar-option').forEach(option => {
                option.classList.remove('selected');
            });
            document.querySelector('.avatar-option[data-avatar="default"]').classList.add('selected');
        }

        function saveQuickPlayer() {
            const name = document.getElementById('quickPlayerName').value.trim();
            const selectedAvatar = document.querySelector('.avatar-option.selected');

            if (!name) {
                Utils.showToast('请输入玩家昵称', 'error');
                document.getElementById('quickPlayerName').focus();
                return;
            }

            if (name.length > 10) {
                Utils.showToast('玩家昵称不能超过10个字符', 'error');
                document.getElementById('quickPlayerName').focus();
                return;
            }

            // 检查昵称是否重复
            const existingPlayers = PlayerManager.getPlayers();
            const isDuplicate = existingPlayers.some(player => player.name === name);

            if (isDuplicate) {
                Utils.showToast('玩家昵称已存在，请使用其他昵称', 'error');
                document.getElementById('quickPlayerName').focus();
                return;
            }

            const avatar = selectedAvatar ? selectedAvatar.dataset.avatar : 'default';

            // 添加新玩家
            const newPlayer = PlayerManager.addPlayer({ name, avatar });
            Utils.showToast('玩家添加成功', 'success');

            // 自动选择新添加的玩家（如果还有空位）
            if (selectedPlayers.length < 3) {
                selectedPlayers.push(newPlayer.id);
                Utils.showToast(`已自动选择玩家"${name}"`, 'info');
            }

            hideQuickAddPlayer();
            loadPlayers();
            updateStartButton();
        }

        // 头像选择事件处理
        document.addEventListener('click', function(e) {
            if (e.target.closest('.avatar-option')) {
                const clickedOption = e.target.closest('.avatar-option');
                const modal = clickedOption.closest('#quickAddPlayerModal');
                if (modal) {
                    modal.querySelectorAll('.avatar-option').forEach(option => {
                        option.classList.remove('selected');
                    });
                    clickedOption.classList.add('selected');
                }
            }
        });

        // 快速添加玩家的回车键支持
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                const quickModal = document.getElementById('quickAddPlayerModal');
                if (quickModal.style.display === 'flex') {
                    saveQuickPlayer();
                }
            }
            if (e.key === 'Escape') {
                hideAddPlayerOptions();
                hideQuickAddPlayer();
            }
        });
    </script>

    <style>
        .player-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #F0F0F0;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .player-item:hover {
            background: rgba(0,0,0,0.02);
        }

        .player-item:last-child {
            border-bottom: none;
        }

        .player-item.selected {
            background: rgba(0,122,255,0.1);
        }

        .player-info {
            flex: 1;
            margin-left: 12px;
        }

        .player-name {
            font-size: 16px;
            font-weight: 600;
        }

        .player-checkbox input[type="checkbox"] {
            display: none;
        }

        .player-checkbox label {
            width: 20px;
            height: 20px;
            border: 2px solid #DDD;
            border-radius: 4px;
            display: block;
            cursor: pointer;
            position: relative;
            transition: all 0.2s;
        }

        .player-checkbox input[type="checkbox"]:checked + label {
            background: #007AFF;
            border-color: #007AFF;
        }

        .player-checkbox input[type="checkbox"]:checked + label::after {
            content: '✓';
            color: white;
            font-size: 12px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .setting-item {
            margin-bottom: 20px;
        }

        .setting-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .setting-select {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #E0E0E0;
            border-radius: 10px;
            font-size: 16px;
            background: white;
        }

        .score-selector {
            display: flex;
            gap: 8px;
        }

        .score-btn {
            flex: 1;
            padding: 12px;
            border: 2px solid #E0E0E0;
            border-radius: 8px;
            background: white;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }

        .score-btn:hover {
            border-color: #007AFF;
        }

        .score-btn.selected {
            background: #007AFF;
            color: white;
            border-color: #007AFF;
        }

        .cap-score-selector {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .cap-score-btn {
            flex: 1;
            padding: 12px;
            border: 2px solid #E0E0E0;
            border-radius: 8px;
            background: white;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }

        .cap-score-btn:hover {
            border-color: #007AFF;
        }

        .cap-score-btn.selected {
            background: #007AFF;
            color: white;
            border-color: #007AFF;
        }

        .game-mode-description, .setting-description {
            margin-top: 8px;
            padding: 8px 12px;
            background: rgba(0, 122, 255, 0.1);
            border-radius: 8px;
            font-size: 12px;
            color: #007AFF;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .player-filter-tabs {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
            padding: 4px;
            background: #F8F9FA;
            border-radius: 12px;
        }

        .filter-tab {
            flex: 1;
            padding: 8px 12px;
            border: none;
            border-radius: 8px;
            background: transparent;
            color: #666;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }

        .filter-tab.active {
            background: white;
            color: #007AFF;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .filter-tab:hover:not(.active) {
            color: #007AFF;
        }

        .multiplier-settings {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .multiplier-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #007AFF;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }
    </style>

    <!-- 添加玩家选项模态框 -->
    <div id="addPlayerOptions" class="add-player-options">
        <div class="add-player-modal">
            <div class="modal-header">
                <h2 class="modal-title">添加玩家</h2>
                <button class="close-btn" onclick="hideAddPlayerOptions()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="add-option" onclick="showQuickAddPlayer()">
                <i class="fas fa-user-plus"></i>
                <div class="add-option-content">
                    <h3>快速添加</h3>
                    <p>直接输入玩家信息快速添加</p>
                </div>
            </div>

            <div class="add-option" onclick="Navigation.goTo('players.html')">
                <i class="fas fa-users-cog"></i>
                <div class="add-option-content">
                    <h3>玩家管理</h3>
                    <p>前往玩家管理页面添加和管理玩家</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速添加玩家模态框 -->
    <div id="quickAddPlayerModal" class="add-player-options">
        <div class="add-player-modal">
            <div class="modal-header">
                <h2 class="modal-title">快速添加玩家</h2>
                <button class="close-btn" onclick="hideQuickAddPlayer()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 8px; font-weight: 600;">玩家昵称</label>
                <input type="text" id="quickPlayerName" placeholder="请输入玩家昵称"
                       style="width: 100%; padding: 12px; border: 1px solid #E0E0E0; border-radius: 8px; font-size: 16px;">
            </div>

            <div style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 8px; font-weight: 600;">选择头像</label>
                <div class="avatar-grid" style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 12px;">
                    <div class="avatar-option selected" data-avatar="default" style="width: 60px; height: 60px; border-radius: 50%; background: #FF6B6B; display: flex; align-items: center; justify-content: center; cursor: pointer; border: 3px solid transparent;">
                        <i class="fas fa-user" style="color: white; font-size: 24px;"></i>
                    </div>
                    <div class="avatar-option" data-avatar="crown" style="width: 60px; height: 60px; border-radius: 50%; background: #FFD700; display: flex; align-items: center; justify-content: center; cursor: pointer; border: 3px solid transparent;">
                        <i class="fas fa-crown" style="color: white; font-size: 24px;"></i>
                    </div>
                    <div class="avatar-option" data-avatar="star" style="width: 60px; height: 60px; border-radius: 50%; background: #4ECDC4; display: flex; align-items: center; justify-content: center; cursor: pointer; border: 3px solid transparent;">
                        <i class="fas fa-star" style="color: white; font-size: 24px;"></i>
                    </div>
                    <div class="avatar-option" data-avatar="heart" style="width: 60px; height: 60px; border-radius: 50%; background: #FF8C94; display: flex; align-items: center; justify-content: center; cursor: pointer; border: 3px solid transparent;">
                        <i class="fas fa-heart" style="color: white; font-size: 24px;"></i>
                    </div>
                </div>
            </div>

            <div style="display: flex; gap: 12px;">
                <button onclick="hideQuickAddPlayer()" style="flex: 1; padding: 12px; border: 1px solid #E0E0E0; background: white; color: #666; border-radius: 8px; cursor: pointer;">
                    取消
                </button>
                <button onclick="saveQuickPlayer()" style="flex: 1; padding: 12px; border: none; background: #007AFF; color: white; border-radius: 8px; cursor: pointer; font-weight: 600;">
                    添加
                </button>
            </div>

            <div style="margin-top: 16px; text-align: center;">
                <button onclick="hideQuickAddPlayer(); Navigation.goTo('players.html')"
                        style="padding: 8px 16px; border: 1px solid #E0E0E0; background: white; color: #666; border-radius: 6px; cursor: pointer; font-size: 14px;">
                    <i class="fas fa-users-cog" style="margin-right: 6px;"></i>
                    玩家管理
                </button>
            </div>
        </div>
    </div>
</body>
</html>
