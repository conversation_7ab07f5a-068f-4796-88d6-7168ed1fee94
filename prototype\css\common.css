/* 斗地主计分App - 通用样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #8B0000 0%, #DC143C 50%, #B22222 100%);
    color: #333;
    overflow-x: hidden;
    user-select: none;
    -webkit-user-select: none;
}

/* iPhone 15 Pro 尺寸 */
.phone-container {
    width: 393px;
    height: 852px;
    margin: 20px auto;
    background: #000;
    border-radius: 47px;
    padding: 2px;
    box-shadow: 0 0 30px rgba(0,0,0,0.5);
    position: relative;
}

.screen {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #8B0000 0%, #DC143C 50%, #B22222 100%);
    border-radius: 45px;
    overflow: hidden;
    position: relative;
}

/* 状态栏 */
.status-bar {
    height: 54px;
    background: rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    color: white;
    font-size: 14px;
    font-weight: 600;
    position: relative;
    z-index: 100;
}

.status-left {
    display: flex;
    align-items: center;
    gap: 5px;
}

.status-right {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* 导航栏 */
.navbar {
    height: 44px;
    background: rgba(255,255,255,0.98);
    backdrop-filter: blur(20px);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    border-bottom: 0.5px solid rgba(0,0,0,0.08);
    position: relative;
    z-index: 99;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.nav-title {
    font-size: 17px;
    font-weight: 700;
    color: #000;
    text-align: center;
    flex: 1;
    letter-spacing: 0.3px;
}

.nav-button {
    background: none;
    border: none;
    color: #007AFF;
    font-size: 17px;
    cursor: pointer;
    padding: 10px;
    border-radius: 8px;
    transition: all 0.2s;
    font-weight: 600;
}

.nav-button:hover {
    background: rgba(0,122,255,0.1);
    transform: scale(1.05);
}

.nav-button.back {
    color: #007AFF;
}

/* 主内容区域 */
.content {
    height: calc(100% - 54px - 44px - 34px);
    overflow-y: auto;
    padding: 20px 16px;
    background: rgba(255,255,255,0.95);
    position: relative;
}

/* Home Indicator */
.home-indicator {
    height: 34px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(0,0,0,0.05);
}

.home-bar {
    width: 134px;
    height: 5px;
    background: rgba(0,0,0,0.3);
    border-radius: 3px;
}

/* 卡片样式 */
.card {
    background: white;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    border: 1px solid rgba(0,0,0,0.05);
}

.card-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
}

/* 按钮样式 */
.btn {
    background: linear-gradient(135deg, #007AFF, #0056CC);
    color: white;
    border: none;
    border-radius: 10px;
    padding: 14px 24px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0,122,255,0.3);
}

.btn:hover {
    background: linear-gradient(135deg, #0056CC, #003D99);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,122,255,0.4);
}

.btn-primary {
    background: linear-gradient(135deg, #DC143C, #B22222);
    box-shadow: 0 2px 8px rgba(220,20,60,0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #B22222, #8B0000);
    box-shadow: 0 4px 12px rgba(220,20,60,0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #6C757D, #5A6268);
    box-shadow: 0 2px 8px rgba(108,117,125,0.3);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #5A6268, #495057);
    box-shadow: 0 4px 12px rgba(108,117,125,0.4);
}

.btn-success {
    background: linear-gradient(135deg, #28A745, #1E7E34);
    box-shadow: 0 2px 8px rgba(40,167,69,0.3);
}

.btn-success:hover {
    background: linear-gradient(135deg, #1E7E34, #155724);
    box-shadow: 0 4px 12px rgba(40,167,69,0.4);
}

.btn-danger {
    background: linear-gradient(135deg, #DC3545, #C82333);
    box-shadow: 0 2px 8px rgba(220,53,69,0.3);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #C82333, #BD2130);
    box-shadow: 0 4px 12px rgba(220,53,69,0.4);
}

.btn-large {
    padding: 18px 32px;
    font-size: 18px;
    border-radius: 12px;
    font-weight: 700;
}

.btn-full {
    width: 100%;
}

/* 输入框样式 */
.input-group {
    margin-bottom: 20px;
}

.input-label {
    display: block;
    font-size: 15px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.input-field {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid #E0E0E0;
    border-radius: 10px;
    font-size: 16px;
    background: white;
    transition: all 0.2s;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.input-field:focus {
    outline: none;
    border-color: #007AFF;
    box-shadow: 0 0 0 4px rgba(0,122,255,0.1), 0 2px 8px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

/* 列表样式 */
.list-item {
    display: flex;
    align-items: center;
    padding: 16px 8px;
    border-bottom: 1px solid #F0F0F0;
    cursor: pointer;
    transition: all 0.2s;
    border-radius: 8px;
    margin-bottom: 4px;
}

.list-item:hover {
    background: rgba(0,0,0,0.02);
    transform: translateX(4px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.list-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

/* 头像样式 */
.avatar {
    width: 40px;
    height: 40px;
    border-radius: 20px;
    background: #F0F0F0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    overflow: hidden;
}

.avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-large {
    width: 60px;
    height: 60px;
    border-radius: 30px;
}

/* 斗地主主题元素 */
.poker-bg {
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y="50" font-size="30" fill="rgba(255,255,255,0.1)">♠♥♣♦</text></svg>') repeat;
    background-size: 100px 100px;
}

.landlord-badge {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #8B0000;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.farmer-badge {
    background: linear-gradient(135deg, #228B22, #32CD32);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* 响应式调整 */
@media (max-width: 400px) {
    .phone-container {
        width: 100%;
        height: 100vh;
        margin: 0;
        border-radius: 0;
    }
    
    .screen {
        border-radius: 0;
    }
}
