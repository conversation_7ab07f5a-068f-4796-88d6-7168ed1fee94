<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>玩家管理 - 斗地主计分助手</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="phone-container">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="status-time">14:30</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <button class="nav-button back" onclick="Navigation.goBack()">
                    <i class="fas fa-chevron-left"></i> 返回
                </button>
                <div class="nav-title">玩家管理</div>
                <button class="nav-button" onclick="showAddPlayerModal()">
                    <i class="fas fa-plus"></i>
                </button>
            </div>

            <!-- 主内容 -->
            <div class="content">
                <!-- 搜索和筛选 -->
                <div class="search-filter-section fade-in">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="playerSearch" placeholder="搜索玩家姓名..." onkeyup="searchPlayers()">
                        <button class="clear-search" onclick="clearSearch()" id="clearSearchBtn" style="display: none;">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="filter-buttons">
                        <button class="filter-btn active" data-filter="all" onclick="filterPlayers('all')">
                            <i class="fas fa-users"></i> 全部
                        </button>
                        <button class="filter-btn" data-filter="favorite" onclick="filterPlayers('favorite')">
                            <i class="fas fa-star"></i> 收藏
                        </button>
                        <button class="filter-btn" data-filter="recent" onclick="filterPlayers('recent')">
                            <i class="fas fa-clock"></i> 最近
                        </button>
                    </div>
                </div>

                <!-- 玩家列表 -->
                <div class="card fade-in">
                    <div class="card-title">
                        <i class="fas fa-users" style="color: #007AFF; margin-right: 8px;"></i>
                        玩家列表
                        <span class="player-count" id="playerCount"></span>
                    </div>
                    <div id="playersList">
                        <!-- 玩家列表将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 添加玩家按钮 -->
                <button class="btn btn-primary btn-large btn-full fade-in" onclick="showAddPlayerModal()">
                    <i class="fas fa-user-plus" style="margin-right: 8px;"></i>
                    添加新玩家
                </button>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator">
                <div class="home-bar"></div>
            </div>
        </div>
    </div>

    <!-- 添加/编辑玩家模态框 -->
    <div id="playerModal" class="modal" style="display: none;">
        <div class="modal-overlay" onclick="hidePlayerModal()"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">添加玩家</h3>
                <button class="modal-close" onclick="hidePlayerModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="input-group">
                    <label class="input-label">玩家昵称</label>
                    <input type="text" id="playerName" class="input-field" placeholder="请输入玩家昵称" maxlength="10">
                </div>
                <div class="input-group">
                    <label class="input-label">选择头像</label>
                    <div class="avatar-selector">
                        <div class="avatar-option" data-avatar="landlord">
                            <div class="avatar avatar-large">
                                <i class="fas fa-crown" style="color: #FFD700; font-size: 24px;"></i>
                            </div>
                            <span>地主</span>
                        </div>
                        <div class="avatar-option" data-avatar="farmer">
                            <div class="avatar avatar-large">
                                <i class="fas fa-user" style="color: #28A745; font-size: 24px;"></i>
                            </div>
                            <span>农民</span>
                        </div>
                        <div class="avatar-option" data-avatar="default">
                            <div class="avatar avatar-large">
                                <i class="fas fa-user-circle" style="color: #6C757D; font-size: 24px;"></i>
                            </div>
                            <span>默认</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hidePlayerModal()">取消</button>
                <button class="btn btn-primary" onclick="savePlayer()">保存</button>
            </div>
        </div>
    </div>

    <style>
        .player-item {
            display: flex;
            align-items: center;
            padding: 16px 0;
            border-bottom: 1px solid #F0F0F0;
            transition: background-color 0.2s;
        }

        .player-item:hover {
            background: rgba(0,0,0,0.02);
            border-radius: 8px;
            margin: 0 -8px;
            padding: 16px 8px;
        }

        .player-item:last-child {
            border-bottom: none;
        }

        .player-info {
            flex: 1;
            margin-left: 4px;
        }

        .player-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }

        .player-meta {
            font-size: 12px;
            color: #666;
        }

        .player-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            background: none;
            border: none;
            padding: 8px;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.2s;
            font-size: 14px;
        }

        .action-btn.edit {
            color: #007AFF;
        }

        .action-btn.edit:hover {
            background: rgba(0,122,255,0.1);
        }

        .action-btn.delete {
            color: #DC3545;
        }

        .action-btn.delete:hover {
            background: rgba(220,53,69,0.1);
        }

        .action-btn.favorite {
            color: #FFD700;
        }

        .action-btn.favorite:hover {
            background: rgba(255, 215, 0, 0.1);
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
        }

        .modal-content {
            background: white;
            border-radius: 16px;
            width: 90%;
            max-width: 400px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
            z-index: 1001;
            box-shadow: 0 8px 24px rgba(0,0,0,0.3);
        }

        .modal-header {
            padding: 24px 24px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 20px;
            color: #666;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .modal-close:hover {
            background: rgba(0,0,0,0.1);
        }

        .modal-body {
            padding: 24px;
        }

        .modal-footer {
            padding: 0 24px 24px;
            display: flex;
            gap: 12px;
        }

        .modal-footer .btn {
            flex: 1;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .input-field {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #E0E0E0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.2s;
        }

        .input-field:focus {
            outline: none;
            border-color: #007AFF;
            box-shadow: 0 0 0 3px rgba(0,122,255,0.1);
        }

        .avatar-selector {
            display: flex;
            gap: 16px;
            justify-content: center;
        }

        .avatar-option {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            padding: 12px;
            border-radius: 8px;
            transition: background-color 0.2s;
        }

        .avatar-option:hover {
            background: rgba(0,0,0,0.05);
        }

        .avatar-option.selected {
            background: rgba(0,122,255,0.1);
        }

        .avatar-option span {
            font-size: 12px;
            color: #666;
            font-weight: 600;
        }

        .empty-players {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        .empty-players i {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
            color: #DC143C;
        }

        .empty-players p {
            margin-bottom: 8px;
            font-size: 16px;
        }

        .empty-players .subtitle {
            font-size: 14px;
            opacity: 0.7;
        }

        .search-filter-section {
            margin-bottom: 20px;
        }

        .search-box {
            position: relative;
            margin-bottom: 12px;
        }

        .search-box i {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            font-size: 14px;
        }

        .search-box input {
            width: 100%;
            padding: 12px 12px 12px 40px;
            border: 1px solid #E0E0E0;
            border-radius: 12px;
            font-size: 16px;
            background: white;
            transition: border-color 0.2s;
        }

        .search-box input:focus {
            outline: none;
            border-color: #007AFF;
            box-shadow: 0 0 0 3px rgba(0,122,255,0.1);
        }

        .clear-search {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #666;
            font-size: 14px;
            padding: 8px;
            cursor: pointer;
            border-radius: 50%;
            transition: background 0.2s;
        }

        .clear-search:hover {
            background: rgba(0,0,0,0.1);
        }

        .filter-buttons {
            display: flex;
            gap: 8px;
            padding: 4px;
            background: #F8F9FA;
            border-radius: 12px;
        }

        .filter-btn {
            flex: 1;
            padding: 8px 12px;
            border: none;
            border-radius: 8px;
            background: transparent;
            color: #666;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }

        .filter-btn.active {
            background: white;
            color: #007AFF;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .filter-btn:hover:not(.active) {
            color: #007AFF;
        }

        .player-count {
            font-size: 12px;
            color: #666;
            font-weight: normal;
        }

        .favorite-star {
            position: absolute;
            top: 4px;
            right: 4px;
            color: #FFD700;
            font-size: 12px;
        }
    </style>

    <script src="js/common.js"></script>
    <script>
        let currentEditingPlayer = null;
        let currentFilter = 'all';
        let searchQuery = '';

        // 页面加载完成后加载玩家列表
        document.addEventListener('DOMContentLoaded', function() {
            loadPlayersList();

            // 监听数据更新事件，实现实时同步
            window.addEventListener('playerDataUpdated', function() {
                loadPlayersList();
            });

            window.addEventListener('gameDataUpdated', function() {
                loadPlayersList(); // 重新加载以更新游戏统计
            });
        });

        function loadPlayersList() {
            const allPlayers = PlayerManager.getPlayers();
            let players = filterAndSearchPlayers(allPlayers);
            const playersListContainer = document.getElementById('playersList');

            // 更新玩家数量显示
            updatePlayerCount(players.length, allPlayers.length);

            if (allPlayers.length === 0) {
                playersListContainer.innerHTML = `
                    <div class="empty-players">
                        <i class="fas fa-user-plus"></i>
                        <p>暂无玩家</p>
                        <p class="subtitle">点击上方按钮添加玩家</p>
                    </div>
                `;
                return;
            }

            if (players.length === 0) {
                playersContainer.innerHTML = `
                    <div class="empty-players">
                        <i class="fas fa-search"></i>
                        <p>没有找到匹配的玩家</p>
                        <p class="subtitle">尝试调整搜索条件或筛选器</p>
                    </div>
                `;
                return;
            }

            playersListContainer.innerHTML = players.map((player, index) => {
                const avatarIcon = getAvatarIcon(player.avatar);
                const gameStats = getPlayerGameStats(player.id);
                const isFavorite = player.isFavorite;
                return `
                    <div class="player-item" data-player-id="${player.id}">
                        <div class="avatar" style="background: ${getPlayerAvatarColor(player)};">
                            ${avatarIcon}
                            ${isFavorite ? '<i class="fas fa-star favorite-star"></i>' : ''}
                        </div>
                        <div class="player-info">
                            <div class="player-name">${player.name}</div>
                            <div class="player-meta">
                                ${gameStats.totalGames}局游戏 • 胜率${gameStats.winRate}% • ${Utils.formatDate(player.createTime)}
                            </div>
                        </div>
                        <div class="player-actions">
                            <button class="action-btn ${isFavorite ? 'favorite' : ''}" onclick="togglePlayerFavorite('${player.id}')" title="${isFavorite ? '取消收藏' : '收藏'}">
                                <i class="fas fa-star"></i>
                            </button>
                            <button class="action-btn edit" onclick="editPlayer('${player.id}')" title="编辑玩家">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="action-btn delete" onclick="confirmDeletePlayer('${player.id}', '${player.name}')" title="删除玩家">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function getPlayerGameStats(playerId) {
            const records = GameRecordManager.getRecords();
            const playerGames = records.filter(record =>
                record.scores.some(score => score.playerId === playerId)
            );

            const totalGames = playerGames.length;
            const wins = playerGames.filter(record => {
                const playerScore = record.scores.find(s => s.playerId === playerId);
                return playerScore && playerScore.score > 0;
            }).length;

            const winRate = totalGames > 0 ? Math.round((wins / totalGames) * 100) : 0;

            return { totalGames, wins, winRate };
        }

        function filterAndSearchPlayers(players) {
            let filteredPlayers = players;

            // 应用筛选器
            switch(currentFilter) {
                case 'favorite':
                    filteredPlayers = players.filter(player => player.isFavorite);
                    break;
                case 'recent':
                    // 获取最近游戏的玩家
                    const recentRecords = GameRecordManager.getRecords().slice(-10);
                    const recentPlayerIds = new Set();
                    recentRecords.forEach(record => {
                        record.scores.forEach(s => recentPlayerIds.add(s.playerId));
                    });
                    filteredPlayers = players.filter(player => recentPlayerIds.has(player.id));
                    break;
                default:
                    filteredPlayers = players;
            }

            // 应用搜索
            if (searchQuery.trim()) {
                filteredPlayers = filteredPlayers.filter(player =>
                    player.name.toLowerCase().includes(searchQuery.toLowerCase())
                );
            }

            return filteredPlayers;
        }

        function searchPlayers() {
            const searchInput = document.getElementById('playerSearch');
            const clearBtn = document.getElementById('clearSearchBtn');

            searchQuery = searchInput.value;

            if (searchQuery.trim()) {
                clearBtn.style.display = 'block';
            } else {
                clearBtn.style.display = 'none';
            }

            loadPlayersList();
        }

        function clearSearch() {
            document.getElementById('playerSearch').value = '';
            document.getElementById('clearSearchBtn').style.display = 'none';
            searchQuery = '';
            loadPlayersList();
        }

        function filterPlayers(filterType) {
            currentFilter = filterType;

            // 更新筛选按钮状态
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-filter="${filterType}"]`).classList.add('active');

            loadPlayersList();
        }

        function updatePlayerCount(filteredCount, totalCount) {
            const countElement = document.getElementById('playerCount');
            if (filteredCount === totalCount) {
                countElement.textContent = `(${totalCount})`;
            } else {
                countElement.textContent = `(${filteredCount}/${totalCount})`;
            }
        }

        function togglePlayerFavorite(playerId) {
            const player = PlayerManager.toggleFavorite(playerId);
            if (player) {
                Utils.showToast(player.isFavorite ? '已添加到收藏' : '已取消收藏', 'success');
                loadPlayersList();
            }
        }

        function getPlayerAvatarColor(player) {
            const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#F8B500', '#FF8C94'];
            const index = player.name.charCodeAt(0) % colors.length;
            return colors[index];
        }

        function getAvatarIcon(avatarType) {
            switch(avatarType) {
                case 'landlord':
                    return '<i class="fas fa-crown" style="color: #FFD700; font-size: 20px;"></i>';
                case 'farmer':
                    return '<i class="fas fa-user" style="color: #28A745; font-size: 20px;"></i>';
                default:
                    return '<i class="fas fa-user-circle" style="color: #6C757D; font-size: 20px;"></i>';
            }
        }

        function showAddPlayerModal() {
            currentEditingPlayer = null;
            document.getElementById('modalTitle').textContent = '添加玩家';
            document.getElementById('playerName').value = '';
            
            // 重置头像选择
            document.querySelectorAll('.avatar-option').forEach(option => {
                option.classList.remove('selected');
            });
            document.querySelector('[data-avatar="default"]').classList.add('selected');
            
            document.getElementById('playerModal').style.display = 'flex';
        }

        function editPlayer(playerId) {
            const player = PlayerManager.getPlayer(playerId);
            if (!player) return;

            currentEditingPlayer = player;
            document.getElementById('modalTitle').textContent = '编辑玩家';
            document.getElementById('playerName').value = player.name;
            
            // 设置头像选择
            document.querySelectorAll('.avatar-option').forEach(option => {
                option.classList.remove('selected');
            });
            document.querySelector(`[data-avatar="${player.avatar || 'default'}"]`).classList.add('selected');
            
            document.getElementById('playerModal').style.display = 'flex';
        }

        function hidePlayerModal() {
            document.getElementById('playerModal').style.display = 'none';
            currentEditingPlayer = null;
        }

        function savePlayer() {
            const name = document.getElementById('playerName').value.trim();
            const selectedAvatar = document.querySelector('.avatar-option.selected');

            if (!name) {
                Utils.showToast('请输入玩家昵称', 'error');
                document.getElementById('playerName').focus();
                return;
            }

            if (name.length > 10) {
                Utils.showToast('玩家昵称不能超过10个字符', 'error');
                document.getElementById('playerName').focus();
                return;
            }

            // 检查昵称是否重复
            const existingPlayers = PlayerManager.getPlayers();
            const isDuplicate = existingPlayers.some(player =>
                player.name === name && (!currentEditingPlayer || player.id !== currentEditingPlayer.id)
            );

            if (isDuplicate) {
                Utils.showToast('玩家昵称已存在，请使用其他昵称', 'error');
                document.getElementById('playerName').focus();
                return;
            }

            const avatar = selectedAvatar ? selectedAvatar.dataset.avatar : 'default';

            if (currentEditingPlayer) {
                // 编辑现有玩家
                PlayerManager.updatePlayer(currentEditingPlayer.id, { name, avatar });
                Utils.showToast('玩家信息已更新');
            } else {
                // 添加新玩家
                PlayerManager.addPlayer({ name, avatar });
                Utils.showToast('玩家添加成功');
            }

            hidePlayerModal();
            loadPlayersList();
        }

        function confirmDeletePlayer(playerId, playerName) {
            const gameStats = getPlayerGameStats(playerId);
            let message = `确定要删除玩家"${playerName}"吗？`;

            if (gameStats.totalGames > 0) {
                message += `\n\n该玩家参与了${gameStats.totalGames}局游戏，删除后相关游戏记录将保留但显示为"已删除玩家"。`;
            }

            Utils.confirm(message, () => {
                deletePlayer(playerId);
            });
        }

        function deletePlayer(playerId) {
            const player = PlayerManager.getPlayer(playerId);
            if (!player) return;

            try {
                PlayerManager.deletePlayer(playerId);
                Utils.showToast('玩家已删除', 'success');
                loadPlayersList();

                // 如果删除后没有玩家了，显示提示
                const remainingPlayers = PlayerManager.getPlayers();
                if (remainingPlayers.length === 0) {
                    Utils.showToast('提示：至少需要3名玩家才能开始游戏', 'info');
                }
            } catch (error) {
                Utils.showToast('删除失败，请重试', 'error');
                console.error('Delete player error:', error);
            }
        }

        // 头像选择事件
        document.addEventListener('click', function(e) {
            if (e.target.closest('.avatar-option')) {
                document.querySelectorAll('.avatar-option').forEach(option => {
                    option.classList.remove('selected');
                });
                e.target.closest('.avatar-option').classList.add('selected');
            }
        });
    </script>

    <style>
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            width: 90%;
            max-width: 350px;
            position: relative;
            z-index: 1001;
            box-shadow: 0 8px 24px rgba(0,0,0,0.3);
        }

        .modal-header {
            padding: 20px 20px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 18px;
            color: #666;
            cursor: pointer;
            padding: 4px;
        }

        .modal-body {
            padding: 20px;
        }

        .modal-footer {
            padding: 0 20px 20px;
            display: flex;
            gap: 12px;
        }

        .modal-footer .btn {
            flex: 1;
        }

        .avatar-selector {
            display: flex;
            gap: 16px;
            justify-content: center;
        }

        .avatar-option {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: background-color 0.2s;
        }

        .avatar-option:hover {
            background: rgba(0,0,0,0.05);
        }

        .avatar-option.selected {
            background: rgba(0,122,255,0.1);
            color: #007AFF;
        }

        .avatar-option span {
            font-size: 12px;
            font-weight: 600;
        }

        .btn-icon {
            background: none;
            border: none;
            padding: 8px;
            cursor: pointer;
            border-radius: 6px;
            transition: background-color 0.2s;
        }

        .btn-icon:hover {
            background: rgba(0,0,0,0.05);
        }
    </style>
</body>
</html>
