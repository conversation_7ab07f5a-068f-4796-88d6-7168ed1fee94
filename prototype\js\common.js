// 斗地主计分App - 通用JavaScript功能

// 本地存储管理
class LocalStorage {
    static setItem(key, value) {
        localStorage.setItem(key, JSON.stringify(value));
    }
    
    static getItem(key) {
        const item = localStorage.getItem(key);
        return item ? JSON.parse(item) : null;
    }
    
    static removeItem(key) {
        localStorage.removeItem(key);
    }
}

// 玩家数据管理
class PlayerManager {
    static getPlayers() {
        return LocalStorage.getItem('players') || [];
    }
    
    static addPlayer(player) {
        const players = this.getPlayers();

        // 生成唯一ID，确保不重复
        let id;
        do {
            id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
        } while (players.some(p => p.id === id));

        player.id = id;
        player.createTime = new Date().toISOString();
        players.push(player);
        LocalStorage.setItem('players', players);

        // 触发数据更新事件
        this.notifyDataUpdate();
        return player;
    }

    static updatePlayer(playerId, updates) {
        const players = this.getPlayers();
        const index = players.findIndex(p => p.id === playerId);
        if (index !== -1) {
            players[index] = { ...players[index], ...updates };
            LocalStorage.setItem('players', players);

            // 触发数据更新事件
            this.notifyDataUpdate();
            return players[index];
        }
        return null;
    }

    static deletePlayer(playerId) {
        const players = this.getPlayers();
        const filtered = players.filter(p => p.id !== playerId);
        LocalStorage.setItem('players', filtered);

        // 触发数据更新事件
        this.notifyDataUpdate();
    }

    static getPlayer(playerId) {
        const players = this.getPlayers();
        return players.find(p => p.id === playerId);
    }

    static toggleFavorite(playerId) {
        const players = this.getPlayers();
        const index = players.findIndex(p => p.id === playerId);
        if (index !== -1) {
            players[index].isFavorite = !players[index].isFavorite;
            LocalStorage.setItem('players', players);

            // 触发数据更新事件
            this.notifyDataUpdate();
            return players[index];
        }
        return null;
    }

    static getFavoritePlayers() {
        const players = this.getPlayers();
        return players.filter(p => p.isFavorite);
    }

    static notifyDataUpdate() {
        // 触发自定义事件通知数据更新
        window.dispatchEvent(new CustomEvent('playerDataUpdated'));
    }
}

// 游戏记录管理
class GameRecordManager {
    static getRecords() {
        return LocalStorage.getItem('gameRecords') || [];
    }
    
    static addRecord(record) {
        const records = this.getRecords();
        record.id = Date.now().toString();
        record.createTime = new Date().toISOString();
        records.unshift(record); // 最新记录在前
        LocalStorage.setItem('gameRecords', records);
        
        // 更新统计数据
        this.updateStatistics(record);
        return record;
    }
    
    static updateStatistics(record) {
        const stats = LocalStorage.getItem('statistics') || {};

        record.players.forEach(playerId => {
            if (!stats[playerId]) {
                stats[playerId] = {
                    totalGames: 0,
                    winGames: 0,
                    landlordGames: 0,
                    landlordWins: 0,
                    totalScore: 0
                };
            }

            const playerStats = stats[playerId];
            playerStats.totalGames++;

            if (playerId === record.landlord) {
                playerStats.landlordGames++;
                if (record.isLandlordWin) {
                    playerStats.winGames++;
                    playerStats.landlordWins++;
                }
            } else {
                if (!record.isLandlordWin) {
                    playerStats.winGames++;
                }
            }

            // 计算得分 - 兼容新旧数据格式
            let playerScore = 0;
            if (record.scores && Array.isArray(record.scores)) {
                const scoreData = record.scores.find(s => s.playerId === playerId);
                playerScore = scoreData ? scoreData.score : 0;
            } else if (record.finalScores) {
                playerScore = record.finalScores[playerId] || 0;
            }
            playerStats.totalScore += playerScore;
        });

        LocalStorage.setItem('statistics', stats);

        // 触发数据更新事件
        this.notifyDataUpdate();
    }

    static deleteRecord(recordId) {
        const records = this.getRecords();
        const filteredRecords = records.filter(r => r.id !== recordId);
        LocalStorage.setItem('gameRecords', filteredRecords);

        // 触发数据更新事件
        this.notifyDataUpdate();
    }

    static getRecordById(recordId) {
        const records = this.getRecords();
        return records.find(r => r.id === recordId);
    }

    static getTodayRecords() {
        const records = this.getRecords();
        const today = new Date().toDateString();
        return records.filter(record => {
            const recordDate = new Date(record.createTime).toDateString();
            return recordDate === today;
        });
    }

    static getPlayerStats(playerId) {
        const records = this.getRecords();
        let totalGames = 0;
        let winGames = 0;
        let totalScore = 0;

        records.forEach(record => {
            if (record.scores) {
                const playerScore = record.scores.find(s => s.playerId === playerId);
                if (playerScore) {
                    totalGames++;
                    totalScore += playerScore.score;
                    if (playerScore.score > 0) winGames++;
                }
            }
        });

        return {
            totalGames,
            winGames,
            totalScore,
            winRate: totalGames > 0 ? Math.round((winGames / totalGames) * 100) : 0
        };
    }

    static notifyDataUpdate() {
        // 触发自定义事件通知数据更新
        window.dispatchEvent(new CustomEvent('gameDataUpdated'));
    }
}

// 游戏设置管理
class GameSettingsManager {
    static getSettings() {
        return LocalStorage.getItem('gameSettings') || {
            gameMode: 'classic',
            baseScore: 1,
            maxMultiplier: 8,
            bombMultiplier: 2,
            springMultiplier: 2
        };
    }
    
    static updateSettings(settings) {
        LocalStorage.setItem('gameSettings', settings);
    }
}

// 通用工具函数
class Utils {
    static formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    static formatTime(date) {
        if (!date) return '';

        const now = new Date();
        const target = new Date(date);

        // 如果是今天，显示时间
        if (now.toDateString() === target.toDateString()) {
            return target.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // 如果是今年，显示月日时间
        if (now.getFullYear() === target.getFullYear()) {
            return target.toLocaleDateString('zh-CN', {
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // 其他情况显示完整日期时间
        return target.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    
    static generateId() {
        return Date.now().toString() + Math.random().toString(36).substr(2, 9);
    }
    
    static showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 100px;
            left: 50%;
            transform: translateX(-50%);
            background: ${type === 'error' ? '#DC3545' : '#28A745'};
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            z-index: 1000;
            font-size: 14px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(-50%) translateY(-20px)';
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 2000);
    }
    
    static confirm(message, callback) {
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1001;
            display: flex;
            align-items: center;
            justify-content: center;
        `;
        
        const modal = document.createElement('div');
        modal.style.cssText = `
            background: white;
            border-radius: 12px;
            padding: 24px;
            max-width: 300px;
            text-align: center;
            box-shadow: 0 8px 24px rgba(0,0,0,0.3);
        `;
        
        modal.innerHTML = `
            <p style="margin-bottom: 20px; font-size: 16px; color: #333;">${message}</p>
            <div style="display: flex; gap: 12px;">
                <button class="btn btn-secondary" style="flex: 1;" onclick="this.closest('.overlay').remove()">取消</button>
                <button class="btn btn-danger" style="flex: 1;" onclick="this.closest('.overlay').remove(); (${callback})()">确认</button>
            </div>
        `;
        
        overlay.className = 'overlay';
        overlay.appendChild(modal);
        document.body.appendChild(overlay);
    }

    static showModal(title, content) {
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        `;

        const modal = document.createElement('div');
        modal.style.cssText = `
            background: white;
            border-radius: 12px;
            padding: 24px;
            max-width: 90%;
            max-height: 80%;
            overflow-y: auto;
            box-shadow: 0 8px 24px rgba(0,0,0,0.3);
        `;

        modal.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                <h3 style="margin: 0; font-size: 18px; color: #333;">${title}</h3>
                <button onclick="this.closest('.overlay').remove()" style="background: none; border: none; font-size: 24px; color: #666; cursor: pointer; padding: 0; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">&times;</button>
            </div>
            <div style="color: #333; line-height: 1.6;">
                ${content}
            </div>
            <div style="margin-top: 20px; text-align: center;">
                <button class="btn btn-primary" onclick="this.closest('.overlay').remove()">确定</button>
            </div>
        `;

        overlay.className = 'overlay';
        overlay.appendChild(modal);
        document.body.appendChild(overlay);

        // 点击背景关闭
        overlay.addEventListener('click', function(e) {
            if (e.target === overlay) {
                overlay.remove();
            }
        });
    }
}

// 页面导航
class Navigation {
    static goTo(page) {
        window.location.href = page;
    }

    static goBack() {
        window.history.back();
    }
}

// 数据同步管理器
class DataSyncManager {
    static init() {
        // 监听存储变化事件，实现跨页面数据同步
        window.addEventListener('storage', function(e) {
            if (e.key === 'players') {
                window.dispatchEvent(new CustomEvent('playerDataUpdated'));
            } else if (e.key === 'gameRecords') {
                window.dispatchEvent(new CustomEvent('gameDataUpdated'));
            } else if (e.key === 'gameSettings') {
                window.dispatchEvent(new CustomEvent('settingsUpdated'));
            }
        });

        // 定期检查数据完整性
        this.validateDataIntegrity();
        setInterval(() => this.validateDataIntegrity(), 30000); // 每30秒检查一次
    }

    static validateDataIntegrity() {
        try {
            // 检查玩家数据完整性
            const players = PlayerManager.getPlayers();
            const records = GameRecordManager.getRecords();

            // 清理无效的游戏记录（引用了不存在的玩家）
            const validPlayerIds = new Set(players.map(p => p.id));
            const validRecords = records.filter(record => {
                return record.players && record.players.every(playerId => validPlayerIds.has(playerId));
            });

            if (validRecords.length !== records.length) {
                console.log(`清理了 ${records.length - validRecords.length} 条无效记录`);
                LocalStorage.setItem('gameRecords', validRecords);
                window.dispatchEvent(new CustomEvent('gameDataUpdated'));
            }


        } catch (error) {
            console.error('Data integrity check failed:', error);
        }
    }



    static syncGameState() {
        // 同步游戏状态到所有相关页面
        const gameSession = LocalStorage.getItem('currentGameSession');
        if (gameSession) {
            window.dispatchEvent(new CustomEvent('gameStateUpdated', {
                detail: gameSession
            }));
        }
    }

    static clearInvalidData() {
        // 清理无效或损坏的数据
        try {
            const keys = ['players', 'gameRecords', 'gameSettings', 'appSettings'];
            keys.forEach(key => {
                const data = LocalStorage.getItem(key);
                if (data === null || data === undefined) {
                    // 重置为默认值
                    switch (key) {
                        case 'players':
                            LocalStorage.setItem(key, []);
                            break;
                        case 'gameRecords':
                            LocalStorage.setItem(key, []);
                            break;
                        case 'gameSettings':
                            LocalStorage.setItem(key, {
                                gameMode: 'classic',
                                baseScore: 1,
                                bombMultiplier: true,
                                springMultiplier: true
                            });
                            break;
                        case 'appSettings':
                            LocalStorage.setItem(key, {
                                themeMode: 'light',
                                enableAnimations: true,
                                enableSounds: true
                            });
                            break;
                    }
                }
            });
        } catch (error) {
            console.error('Clear invalid data failed:', error);
        }
    }
}

// 初始化默认数据
function initializeDefaultData() {
    const players = PlayerManager.getPlayers();

    // 检查是否有重复ID的玩家，如果有则清除所有数据重新创建
    const playerIds = players.map(p => p.id);
    const hasDuplicateIds = playerIds.length !== new Set(playerIds).size;

    if (players.length === 0 || hasDuplicateIds) {
        console.log('Initializing default players...');

        // 清除现有数据
        LocalStorage.removeItem('players');

        // 创建默认玩家，直接设置到localStorage
        const defaultPlayers = [
            {
                id: Date.now().toString() + '_1',
                name: '张三',
                avatar: 'landlord',
                createTime: new Date().toISOString()
            },
            {
                id: Date.now().toString() + '_2',
                name: '李四',
                avatar: 'farmer',
                createTime: new Date().toISOString()
            },
            {
                id: Date.now().toString() + '_3',
                name: '王五',
                avatar: 'farmer',
                createTime: new Date().toISOString()
            }
        ];

        LocalStorage.setItem('players', defaultPlayers);
        PlayerManager.notifyDataUpdate();
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeDefaultData();
    
    // 更新状态栏时间
    updateStatusBar();
    setInterval(updateStatusBar, 60000); // 每分钟更新一次
});

function updateStatusBar() {
    const timeElement = document.querySelector('.status-time');
    if (timeElement) {
        const now = new Date();
        timeElement.textContent = now.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    updateStatusBar();
    setInterval(updateStatusBar, 60000); // 每分钟更新一次时间

    // 初始化默认数据
    initializeDefaultData();

    // 初始化数据同步管理器
    DataSyncManager.init();
});
