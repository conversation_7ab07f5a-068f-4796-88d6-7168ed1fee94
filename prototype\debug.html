<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试页面</title>
</head>
<body>
    <h1>localStorage 调试信息</h1>
    <div id="debug-info"></div>
    
    <script src="js/common.js"></script>
    <script>
        function displayDebugInfo() {
            const debugDiv = document.getElementById('debug-info');
            
            const info = {
                'players': LocalStorage.getItem('players'),
                'currentGameSettings': LocalStorage.getItem('currentGameSettings'),
                'currentGamePlayers': LocalStorage.getItem('currentGamePlayers'),
                'currentGameSession': LocalStorage.getItem('currentGameSession'),
                'gameHistories': LocalStorage.getItem('gameHistories')
            };
            
            let html = '';
            for (const [key, value] of Object.entries(info)) {
                html += `<h3>${key}:</h3>`;
                html += `<pre>${JSON.stringify(value, null, 2)}</pre>`;
            }
            
            debugDiv.innerHTML = html;
        }
        
        document.addEventListener('DOMContentLoaded', displayDebugInfo);
    </script>
</body>
</html>
